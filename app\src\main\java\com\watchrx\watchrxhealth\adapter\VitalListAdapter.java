package com.watchrx.watchrxhealth.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.VitalDataModel;
import com.watchrx.watchrxhealth.utils.DateUtils;
import com.watchrx.watchrxhealth.utils.OnItemClickListener;

import java.util.List;

public class VitalListAdapter extends RecyclerView.Adapter<VitalListAdapter.VitalListViewHolder> {

    private final List<VitalDataModel> vitalList;
    private OnItemClickListener listener;

//    public interface OnItemClickListener {
//        void onItemClick(VitalDataModel item);
//    }


    public VitalListAdapter(List<VitalDataModel> vitalList, OnItemClickListener listener) {
        this.vitalList = vitalList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public VitalListAdapter.VitalListViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.vital_list_item, parent, false);
        return new VitalListAdapter.VitalListViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull VitalListAdapter.VitalListViewHolder holder, int position) {
        VitalDataModel dataModel = vitalList.get(position);
        holder.title.setText(dataModel.getVitalType());
        holder.mainValue.setText(dataModel.getVitalData());
        if (dataModel.getMeasureDateTime() != null && !dataModel.getMeasureDateTime().isEmpty()) {
            String formattedDate = DateUtils.formatDateString(
                    dataModel.getMeasureDateTime(),
                    "yyyy-MM-dd HH:mm:ss",
                    "dd MMM yyyy, h:mm a"
            );
            holder.date_time.setText(formattedDate);
        } else {
            holder.date_time.setText("--");
        }
        int imageResId = dataModel.getImage();
        holder.imageView.setImageResource(imageResId);

        // Hide "Collect Now" button for Health Connect entries
        if (dataModel.getVitalType().contains("Health Connect")) {
            holder.button.setVisibility(View.GONE);
        } else {
            holder.button.setVisibility(View.VISIBLE);
            holder.button.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onButtonClicked(dataModel);
                }
            });
        }

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onItemClicked(dataModel);
            }
        });
    }


    @Override
    public int getItemCount() {
        return vitalList != null ? vitalList.size() : 0;
    }

    public static class VitalListViewHolder extends RecyclerView.ViewHolder {
        TextView title, mainValue, date_time;
        ImageView imageView;
        Button button;

        public VitalListViewHolder(@NonNull View itemView) {
            super(itemView);
            title = itemView.findViewById(R.id.title);
            mainValue = itemView.findViewById(R.id.mainValue);
            date_time = itemView.findViewById(R.id.date_time);
            imageView = itemView.findViewById(R.id.imageType);
            button = itemView.findViewById(R.id.collectNow);
        }
    }
}
