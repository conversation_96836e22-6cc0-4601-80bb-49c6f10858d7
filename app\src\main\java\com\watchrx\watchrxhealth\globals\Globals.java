package com.watchrx.watchrxhealth.globals;

import android.app.PendingIntent;
import android.media.MediaPlayer;
import android.speech.tts.TextToSpeech;

import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.location.LocationRequest;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.QueueComparator;

import java.util.HashMap;
import java.util.Map;
import java.util.PriorityQueue;

public class Globals {

    public static TextToSpeech textToSpeech;

    public static String imei;

    final public static String synchronized_were = "synchronizing";

    public static int reminderAlarmCount = 1;

    public static Map<String, Boolean> timersCurrentlySetMap = new HashMap<>();

    public static MediaPlayer player = new MediaPlayer();

    public static Map<String, PendingIntent> intentMap = new HashMap<>();

    public static Map<String, String> alarmCorrelationIdMap = new HashMap<>();

    public static PendingIntent dailyScheduleUpdater = null;

    public static PendingIntent dailySyncUpdater = null;

    public static PendingIntent dailySyncUpdaterat11 = null;

    public static PendingIntent dailySyncUpdaterat16 = null;

    public static PendingIntent dailyScheduleMidnightUpdater = null;

    public static Boolean alreadySpeaking = false;

    public static PendingIntent reminderAlertTimeoutIntent;

    public static PendingIntent gpsFencePendingIntent;

    public static boolean isWatchRegistered = false;

    public static boolean isServerDataSame = false;

    public static boolean isDownloading = false;

    public static LocationRequest locationRequest;

    public static GoogleApiClient googleApiClient;

    public static boolean isCrossed = false;

    public static Double latitude = 0.0;

    public static Double longitude = 0.0;

    public static String fromDefencive = "";

    public static boolean firstTimeCrossed = false;

    public static PriorityQueue<ActivityInfoForQueue> priorityQueue = new PriorityQueue<>(20, new QueueComparator());

    public static boolean isScreenRunning = false;

    public static long startCallTime;

    public static long endCallTime;

    public static PendingIntent networkHeartBeat = null;

    public static PendingIntent pedoMeter = null;
    public static PendingIntent pedoResetMeter = null;
    public static PendingIntent midnightLogFileUpload = null;
    public static PendingIntent collectVitalsFromDeviceEvery4Hour = null;

    public static PendingIntent queueCheckReminderPendingIntent = null;

    public static Map<String, PendingIntent> vitalsIntentMap = new HashMap<>();
    public static Map<String, Boolean> vitalsTimersCurrentlySetMap = new HashMap<>();


    public static String authToken = null;
}
