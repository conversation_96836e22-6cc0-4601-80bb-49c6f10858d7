-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:2:1-407:12
INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:2:1-407:12
MERGED from [com.github.GoodieBag:Pinview:v1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b86d17340e149f32b42d8e4b4155161e\transformed\jetified-Pinview-v1.4\AndroidManifest.xml:2:1-16:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee139caef85bfed62fe92dd3a70d103a\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.applandeo:material-calendar-view:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-3\c94defda84e4aea81c759dab29875d1f\transformed\jetified-material-calendar-view-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f533fcbc46a57fb9ff7758295e66825\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\98720b29c7f34fbfef5d30a8961d4610\transformed\preference-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:2:1-14:12
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:2:1-62:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\323bed96daa27a02359b2078b79c05c6\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\42fcd71a3362c3d3c804d610cf3f2c72\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5bf5e5c6ef9e8b277937fda46aabee7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.amazonaws:aws-android-sdk-lex:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\8389f4045f4fdc52a660db09d8f5a44a\transformed\jetified-aws-android-sdk-lex-2.16.13\AndroidManifest.xml:2:1-8:12
MERGED from [com.amazonaws:aws-android-sdk-mobile-client:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\95dc6764ddc16f8cca6dc25b8b4f1d65\transformed\jetified-aws-android-sdk-mobile-client-2.16.13\AndroidManifest.xml:2:1-7:12
MERGED from [com.amazonaws:aws-android-sdk-auth-core:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\4c345bdfac77b4b56aa7e015904465a4\transformed\jetified-aws-android-sdk-auth-core-2.16.13\AndroidManifest.xml:2:1-4:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaf23df0ef1ecaa7e75b8d117c11c175\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\497163c371a489f5abc521cbbdd03f6a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16898ebf9e4c90c191a5cd0a61417114\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e2e54c84504d4b5d4bbcd5b5a658108\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\acfcc9326a00e293517549780da1e94b\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\182ddcdfdcce3e4ca41e0673f1f70b41\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c33b659fd63b325502dfc0b524f9bdc4\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c742509490d53adfb7875f5ea1fbf\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03e0667da5aa10808c0b497f6ef59386\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\57c205b2a69818851dbd59e42765446d\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f12f4461742a793f75d02b9f893a8a05\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.work:work-runtime-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\2da4482590d8aff2abab2cb29a9498f8\transformed\work-runtime-ktx-2.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.gridlayout:gridlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc65449114092406ba100a0c99207fb8\transformed\gridlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3e224de2962769b02daf67946df0276\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\357ca665e17609b0c4b22bac231a4d55\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\50f4bdfa866f097bcb5f1b8e94eac5ee\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\975134eed933950f502dc0138ce9ae15\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\7b45cd5be285aa2c7bb9d6f23bd2e316\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\116096bb688bc1e303194276f340877f\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5298376b6a3b07f76e39da2b00fe6abc\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c496671a68b8e9628512bef2587c7af2\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a77c5ae137c73c8a2517bce2f0159341\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\00b602cc22e24b38e469887e456c028e\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e9cc04b6b1c9480909f53dcb2c61f88\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29611d231b6306739a8efd62e51a3795\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\752d9797f158c0fdb0f3caeb19a14651\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeaefd5da97e94ec3fc3cd55867cde2e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bab69c72513635a63b5299e261d854f\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\06aa9dc8f505a27c5233ca7991a98d55\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd989bbca9253df37428ca87de178143\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\76cb69c2fe39df612498a1c977b35b87\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ace4240242d7e30916f29befa3ff2d25\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\e81b820efb681ef4f53988c1afa8d787\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfc6b2dc1b1e8d7695a962a47974e93a\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b0edbd2979ae96216d8e5e558dae44e\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\267985dc04d40e215387f1f013ce0e5f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\965e73c60b960f9618dcd798b95c371f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\39daceb33ba91659cedff614c22a6e5c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6450e1e5d34d387e191f2f0f9c5324df\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4119d5db31bc69bce75ff5007ce4ae7\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\951b9fed77767cfb0d7e80606a592bbf\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7396de89cd5cec42bf887812134173\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e321fe3f4c17167c92ba96d58d370984\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\edb8a9842af09e8ef63a8283d4e57e4a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\323e1c48885638197f1bc4278940a379\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\7ed42c12cb279249327ac70e76bb72eb\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eefc612161d90b6bd0031304794c24b9\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05513f78504591973ae7f1848436c86a\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.twilio:audioswitch:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\903a21fdcfea6838d8c1375fe6560388\transformed\jetified-audioswitch-1.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:2:1-16:12
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d574bc0f4bbe001d4fafcf10e48cd68c\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c70b5acd25a6a846e95150d61b579a9d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\805004bfd7065da2889542de939ea64d\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\390f4f6c902a60bf590fca236a0f2505\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ede0399808c17d9b7f9da6bb0e818d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75d3b07ab426e8f0c2820d7b9009f233\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a8ac7cea3fe20e30fac01c2af47cdcb\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa8517b5200bb3d0892b6a6801022804\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-3\7c08fbcf380424f7a04ebeb3b8f331a4\transformed\jetified-security-crypto-1.1.0-alpha03\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f87b76aeadea9247567ecb3d965f4355\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5c40d3058318171a542817b0be734bf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac93541a53afe9cc247064049ea1b347\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e9c34f910da9af6bf1f69f00c116090\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f68016607b3392c6493a8cc16928621c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cf342c09250091525de2fd613e4977e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4133846e7f2e2644ba88403abc9ba3ee\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df9fb09667536424562f03385f007d84\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\bbd7d3144b85d65325b6aea7a855990b\transformed\jetified-relinker-1.4.4\AndroidManifest.xml:2:1-7:12
	package
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:4:5-40
		INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:6:5-8:40
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:7:5-9:36
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:7:5-9:36
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:10:5-12:36
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:10:5-12:36
	tools:ignore
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:8:9-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:7:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:9:5-70
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:13:5-15:36
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:13:5-15:36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:9:19-67
uses-feature#0x00020000
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:10:5-12:35
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:9:5-54
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:9:5-54
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:11:9-41
	android:required
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:12:9-32
		MERGED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:12:9-32
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:14:5-77
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:54:5-77
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:54:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:14:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:15:5-92
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:59:5-92
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:59:5-92
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:15:22-89
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:16:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:16:22-76
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:17:5-79
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:28:5-79
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:28:5-79
MERGED from [com.amazonaws:aws-android-sdk-lex:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\8389f4045f4fdc52a660db09d8f5a44a\transformed\jetified-aws-android-sdk-lex-2.16.13\AndroidManifest.xml:5:5-78
MERGED from [com.amazonaws:aws-android-sdk-lex:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\8389f4045f4fdc52a660db09d8f5a44a\transformed\jetified-aws-android-sdk-lex-2.16.13\AndroidManifest.xml:5:5-78
MERGED from [com.amazonaws:aws-android-sdk-mobile-client:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\95dc6764ddc16f8cca6dc25b8b4f1d65\transformed\jetified-aws-android-sdk-mobile-client-2.16.13\AndroidManifest.xml:5:5-78
MERGED from [com.amazonaws:aws-android-sdk-mobile-client:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\95dc6764ddc16f8cca6dc25b8b4f1d65\transformed\jetified-aws-android-sdk-mobile-client-2.16.13\AndroidManifest.xml:5:5-78
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:11:5-79
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:11:5-79
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:10:5-79
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:17:22-76
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:18:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:19:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:19:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:20:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:21:5-76
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:29:5-76
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:29:5-76
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:12:5-76
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:12:5-76
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:11:5-76
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:11:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:21:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:22:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:22:22-73
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:23:5-67
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:27:5-67
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:27:5-67
MERGED from [com.amazonaws:aws-android-sdk-lex:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\8389f4045f4fdc52a660db09d8f5a44a\transformed\jetified-aws-android-sdk-lex-2.16.13\AndroidManifest.xml:6:5-66
MERGED from [com.amazonaws:aws-android-sdk-lex:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\8389f4045f4fdc52a660db09d8f5a44a\transformed\jetified-aws-android-sdk-lex-2.16.13\AndroidManifest.xml:6:5-66
MERGED from [com.amazonaws:aws-android-sdk-mobile-client:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\95dc6764ddc16f8cca6dc25b8b4f1d65\transformed\jetified-aws-android-sdk-mobile-client-2.16.13\AndroidManifest.xml:4:5-66
MERGED from [com.amazonaws:aws-android-sdk-mobile-client:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\95dc6764ddc16f8cca6dc25b8b4f1d65\transformed\jetified-aws-android-sdk-mobile-client-2.16.13\AndroidManifest.xml:4:5-66
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:28:5-67
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:28:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:9:5-67
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:9:5-67
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:7:5-67
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:23:22-64
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:24:22-76
uses-permission#android.permission.CHANGE_WIFI_MULTICAST_STATE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:25:5-86
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:25:22-83
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:26:5-75
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:32:5-75
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:32:5-75
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:26:22-72
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:27:5-66
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:27:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:28:5-68
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:47:5-68
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:47:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:29:5-68
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:29:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:28:22-65
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:29:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:29:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:30:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:30:22-78
uses-permission#android.permission.CALL_PHONE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:31:5-69
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:31:22-66
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:32:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:32:22-78
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:33:5-80
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:39:5-80
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:39:5-80
MERGED from [com.twilio:audioswitch:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\903a21fdcfea6838d8c1375fe6560388\transformed\jetified-audioswitch-1.2.0\AndroidManifest.xml:7:5-80
MERGED from [com.twilio:audioswitch:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\903a21fdcfea6838d8c1375fe6560388\transformed\jetified-audioswitch-1.2.0\AndroidManifest.xml:7:5-80
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:10:5-80
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:10:5-80
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:9:5-80
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:9:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:33:22-77
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:34:5-85
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:34:22-82
uses-permission#android.permission.BODY_SENSORS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:35:5-71
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:35:22-68
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:36:5-77
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:53:5-77
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:53:5-77
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:36:22-74
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:37:5-65
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:11:5-65
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:11:5-65
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:36:5-65
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:36:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:37:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:38:5-71
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:12:5-71
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:12:5-71
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:33:5-71
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:33:5-71
MERGED from [com.amazonaws:aws-android-sdk-lex:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\8389f4045f4fdc52a660db09d8f5a44a\transformed\jetified-aws-android-sdk-lex-2.16.13\AndroidManifest.xml:4:5-70
MERGED from [com.amazonaws:aws-android-sdk-lex:2.16.13] C:\Users\<USER>\.gradle\caches\transforms-3\8389f4045f4fdc52a660db09d8f5a44a\transformed\jetified-aws-android-sdk-lex-2.16.13\AndroidManifest.xml:4:5-70
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:8:5-71
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:8:5-71
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:38:22-68
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:39:5-41:38
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:40:5-68
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:40:5-68
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:41:9-35
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:40:9-52
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:42:5-44:38
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:41:5-74
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:41:5-74
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:44:9-35
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:43:9-58
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:45:5-47:58
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:43:5-73
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:43:5-73
	android:usesPermissionFlags
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:47:9-55
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:46:9-57
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:48:5-76
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:42:5-76
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:42:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:48:22-73
uses-permission#android.permission.health.READ_STEPS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:51:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:51:22-73
uses-permission#android.permission.health.WRITE_STEPS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:52:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:52:22-74
queries
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:55:5-57:15
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:30:5-34:15
package#com.google.android.apps.healthdata
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:56:9-70
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:56:18-67
uses-feature#android.hardware.bluetooth_le
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:59:5-61:36
	android:required
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:61:9-33
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:60:9-53
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:63:5-65:31
REJECTED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:58:5-94
	tools:node
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:65:9-28
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:64:9-78
application
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:67:5-405:19
MERGED from [com.github.GoodieBag:Pinview:v1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b86d17340e149f32b42d8e4b4155161e\transformed\jetified-Pinview-v1.4\AndroidManifest.xml:11:5-14:19
MERGED from [com.github.GoodieBag:Pinview:v1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b86d17340e149f32b42d8e4b4155161e\transformed\jetified-Pinview-v1.4\AndroidManifest.xml:11:5-14:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee139caef85bfed62fe92dd3a70d103a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee139caef85bfed62fe92dd3a70d103a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f533fcbc46a57fb9ff7758295e66825\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f533fcbc46a57fb9ff7758295e66825\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:31:5-41:19
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:31:5-41:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\497163c371a489f5abc521cbbdd03f6a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\497163c371a489f5abc521cbbdd03f6a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:36:5-42:19
MERGED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c33b659fd63b325502dfc0b524f9bdc4\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c33b659fd63b325502dfc0b524f9bdc4\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c742509490d53adfb7875f5ea1fbf\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c742509490d53adfb7875f5ea1fbf\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03e0667da5aa10808c0b497f6ef59386\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03e0667da5aa10808c0b497f6ef59386\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\57c205b2a69818851dbd59e42765446d\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\57c205b2a69818851dbd59e42765446d\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\76cb69c2fe39df612498a1c977b35b87\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\76cb69c2fe39df612498a1c977b35b87\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ace4240242d7e30916f29befa3ff2d25\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ace4240242d7e30916f29befa3ff2d25\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:14:5-20
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:14:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ede0399808c17d9b7f9da6bb0e818d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ede0399808c17d9b7f9da6bb0e818d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cf342c09250091525de2fd613e4977e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cf342c09250091525de2fd613e4977e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:77:9-52
	android:extractNativeLibs
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:72:9-41
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:81:9-51
	tools:ignore
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:82:9-48
	android:icon
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:74:9-54
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:71:9-31
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:78:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:75:9-41
	android:appComponentFactory
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:70:9-55
		REJECTED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	tools:overrideLibrary
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:83:9-54
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:73:9-42
	android:persistent
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:76:9-34
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:69:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:79:9-45
	tools:replace
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:84:9-52
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:80:9-44
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:68:9-33
activity#com.watchrx.watchrxhealth.LatestAlertsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:85:9-87:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:87:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:86:13-49
activity#com.watchrx.watchrxhealth.ZoomVideoCallScreen
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:88:9-90:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:90:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:89:13-48
activity#com.watchrx.watchrxhealth.auth.EnterOTPActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:91:9-93:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:93:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:92:13-50
activity#com.watchrx.watchrxhealth.auth.EnterPasswordActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:94:9-96:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:96:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:95:13-55
activity#com.watchrx.watchrxhealth.auth.ResendOTPScreen
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:98:9-100:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:100:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:99:13-49
activity#com.watchrx.watchrxhealth.auth.LoginScreen
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:101:9-113:20
	android:singleUser
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:106:13-38
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:105:13-42
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:104:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:103:13-81
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:107:13-33
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:102:13-45
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:108:13-112:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:109:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:109:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:111:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:111:27-74
activity#com.watchrx.watchrxhealth.LoginActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:114:9-116:37
	tools:ignore
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:116:13-34
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:115:13-42
activity#com.watchrx.watchrxhealth.ChatActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:117:9-119:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:119:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:118:13-41
activity#com.watchrx.watchrxhealth.VitalsGraphActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:120:9-122:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:122:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:121:13-48
activity#com.watchrx.watchrxhealth.MyTaskCalendar
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:123:9-125:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:125:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:124:13-43
activity#com.watchrx.watchrxhealth.WebViewActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:126:9-128:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:128:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:127:13-44
activity#com.watchrx.watchrxhealth.ReminderDetailsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:129:9-131:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:131:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:130:13-52
activity#com.watchrx.watchrxhealth.VitalDashboard
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:132:9-134:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:134:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:133:13-43
activity#com.watchrx.watchrxhealth.InteractiveVoiceActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:135:9-137:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:137:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:136:13-53
activity#com.watchrx.watchrxhealth.PatientDiaryActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:138:9-140:54
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:140:13-51
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:139:13-49
activity#com.watchrx.watchrxhealth.SleepMonitorActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:141:9-58
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:141:19-55
activity#com.watchrx.watchrxhealth.SplashActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:142:9-52
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:142:19-49
activity#com.watchrx.watchrxhealth.VitalDetailsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:143:9-145:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:145:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:144:13-49
activity#com.watchrx.watchrxhealth.ViewAllTextMessageActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:146:9-148:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:148:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:147:13-55
activity#com.watchrx.watchrxhealth.MedicationDetailsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:149:9-151:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:151:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:150:13-54
activity#com.watchrx.watchrxhealth.GPSActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:152:9-154:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:154:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:153:13-40
activity#com.watchrx.watchrxhealth.TextMessageActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:155:9-158:70
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:157:13-49
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:158:13-67
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:156:13-48
activity#com.watchrx.watchrxhealth.PhoneCallsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:159:9-161:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:161:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:160:13-47
activity#com.watchrx.watchrxhealth.WifiConfig
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:162:9-48
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:162:19-45
activity#com.watchrx.watchrxhealth.CustomAlertActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:163:9-165:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:165:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:164:13-48
activity#com.watchrx.watchrxhealth.MainActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:166:9-169:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:169:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:168:13-44
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:167:13-41
activity#com.watchrx.watchrxhealth.ReminderActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:170:9-172:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:172:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:171:13-45
activity#com.watchrx.watchrxhealth.AddMedication
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:173:9-176:58
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:175:13-49
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:176:13-55
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:174:13-42
activity#com.watchrx.watchrxhealth.MedicationActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:177:9-179:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:179:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:178:13-47
activity#com.watchrx.watchrxhealth.NurseOnTheWayActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:180:9-182:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:182:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:181:13-50
activity#com.watchrx.watchrxhealth.VisitVerificationActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:183:9-185:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:185:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:184:13-54
activity#com.watchrx.watchrxhealth.BatteryActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:186:9-188:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:188:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:187:13-44
activity#com.watchrx.watchrxhealth.AlertsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:189:9-191:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:191:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:190:13-43
activity#com.watchrx.watchrxhealth.HeartRateActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:192:9-194:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:194:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:193:13-46
activity#com.watchrx.watchrxhealth.PedoMeterActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:195:9-197:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:197:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:196:13-46
activity#com.watchrx.watchrxhealth.ScheduleTextMessageActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:200:9-202:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:202:13-49
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:201:13-56
activity#com.watchrx.watchrxhealth.twilio.VideoActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:203:9-206:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:206:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:205:13-44
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:204:13-49
activity#com.watchrx.watchrxhealth.twilio.SettingsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:207:9-210:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:210:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:209:13-44
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:208:13-52
activity#com.watchrx.watchrxhealth.ble.NewVitalsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:211:9-59
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:211:19-56
activity#com.watchrx.watchrxhealth.voip.IncomingCallActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:212:9-214:47
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:214:13-44
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:213:13-54
activity#com.watchrx.watchrxhealth.HealthVitalsActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:215:9-223:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:218:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:217:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:216:13-49
intent-filter#action:name:android.intent.action.VIEW_PERMISSION_USAGE+category:name:android.intent.category.HEALTH_PERMISSIONS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:219:13-222:29
action#android.intent.action.VIEW_PERMISSION_USAGE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:220:17-85
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:220:25-83
category#android.intent.category.HEALTH_PERMISSIONS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:221:17-86
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:221:27-84
activity#com.watchrx.watchrxhealth.PermissionsRationaleActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:225:9-232:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:228:13-49
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:227:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:226:13-57
intent-filter#action:name:androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:229:13-231:29
action#androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:230:17-92
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:230:25-89
activity-alias#com.watchrx.watchrxhealth.ViewPermissionUsageActivity
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:235:9-244:26
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:237:13-36
	android:targetActivity
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:238:13-67
	android:permission
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:239:13-80
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:236:13-55
meta-data#com.google.android.gms.version
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:246:9-248:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ace4240242d7e30916f29befa3ff2d25\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ace4240242d7e30916f29befa3ff2d25\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:248:13-66
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:247:13-58
meta-data#firebase_messaging_auto_init_enabled
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:249:9-251:37
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:251:13-34
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:250:13-64
meta-data#firebase_analytics_collection_enabled
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:252:9-254:37
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:254:13-34
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:253:13-65
meta-data#com.google.android.geo.API_KEY
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:255:9-257:71
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:257:13-68
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:256:13-58
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:258:9-260:71
	android:value
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:260:13-68
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:259:13-89
provider#androidx.core.content.FileProvider
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:262:9-270:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:266:13-47
	android:authorities
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:264:13-73
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:265:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:263:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:267:13-269:53
	android:resource
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:269:17-50
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:268:17-67
receiver#com.watchrx.watchrxhealth.receivers.CheckQueueReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:272:9-275:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:274:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:275:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:273:13-57
receiver#com.watchrx.watchrxhealth.receivers.ReminderReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:276:9-279:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:278:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:279:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:277:13-55
receiver#com.watchrx.watchrxhealth.receivers.CustomAlertReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:280:9-283:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:282:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:283:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:281:13-58
receiver#com.watchrx.watchrxhealth.syncup.MedicationScheduleSetupReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:284:9-287:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:286:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:287:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:285:13-67
receiver#com.watchrx.watchrxhealth.syncup.SyncupReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:288:9-291:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:290:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:291:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:289:13-50
receiver#com.watchrx.watchrxhealth.receivers.RebootComplete
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:292:9-304:20
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:294:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:295:13-36
	android:permission
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:296:13-75
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:293:13-53
intent-filter#action:name:android.intent.action.ACTION_SHUTDOWN+action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:297:13-303:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:298:17-79
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:298:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:299:17-82
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:299:25-79
action#android.intent.action.ACTION_SHUTDOWN
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:300:17-80
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:300:25-77
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:302:17-76
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:302:27-73
receiver#com.watchrx.watchrxhealth.receivers.TimeZoneChangedReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:9-312:20
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:307:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:308:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:306:13-62
intent-filter#action:name:android.intent.action.TIMEZONE_CHANGED
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:309:13-311:29
action#android.intent.action.TIMEZONE_CHANGED
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:310:17-81
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:310:25-78
receiver#com.watchrx.watchrxhealth.receivers.NewPackageInstalled
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:313:9-327:20
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:315:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:316:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:314:13-58
intent-filter#action:name:android.intent.action.PACKAGE_ADDED+action:name:android.intent.action.PACKAGE_INSTALL+action:name:android.intent.action.PACKAGE_REMOVED+action:name:android.intent.action.PACKAGE_REPLACED+category:name:android.intent.category.DEFAULT+data:scheme:package
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:317:13-326:29
action#android.intent.action.PACKAGE_ADDED
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:318:17-78
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:318:25-75
action#android.intent.action.PACKAGE_INSTALL
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:319:17-80
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:319:25-77
action#android.intent.action.PACKAGE_REMOVED
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:320:17-80
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:320:25-77
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:321:17-81
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:321:25-78
data
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:325:17-50
	android:scheme
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:325:23-47
receiver#com.watchrx.watchrxhealth.receivers.InternetStatusChangeReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:328:9-335:20
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:330:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:329:13-67
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:331:13-334:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:332:17-79
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:332:25-76
receiver#com.watchrx.watchrxhealth.receivers.HrtBroadcastReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:336:9-339:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:338:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:339:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:337:13-59
receiver#com.watchrx.watchrxhealth.receivers.NetworkHeartBeat
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:340:9-343:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:342:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:343:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:341:13-55
receiver#com.watchrx.watchrxhealth.receivers.ScheduleMessageReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:344:9-347:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:346:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:347:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:345:13-62
receiver#com.watchrx.watchrxhealth.receivers.SendPedoMeterReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:348:9-351:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:350:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:351:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:349:13-60
receiver#com.watchrx.watchrxhealth.receivers.MidnightPedoMeterResetReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:352:9-355:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:354:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:355:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:353:13-69
receiver#com.watchrx.watchrxhealth.receivers.VitalReminderReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:356:9-359:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:358:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:359:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:357:13-60
receiver#com.watchrx.watchrxhealth.receivers.MidnightLogFileUploadReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:360:9-363:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:362:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:363:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:361:13-68
receiver#com.watchrx.watchrxhealth.receivers.BleBroadCastReceiver
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:364:9-367:39
	android:enabled
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:366:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:367:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:365:13-59
service#com.watchrx.watchrxhealth.gcm.GCMPushReceiverService
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:369:9-379:19
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:372:13-36
	tools:ignore
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:374:13-48
	android:permission
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:373:13-38
	android:directBootAware
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:371:13-43
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:370:13-55
intent-filter#action:name:com.google.firebase.INSTANCE_ID_EVENT+action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:375:13-378:29
action#com.google.firebase.INSTANCE_ID_EVENT
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:376:17-80
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:376:25-77
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:377:17-78
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:377:25-75
service#com.watchrx.watchrxhealth.gcm.GCMRegistrationIntentService
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:380:9-382:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:382:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:381:13-61
service#com.watchrx.watchrxhealth.gps.GeoFenceIntentServices
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:383:9-385:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:385:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:384:13-55
service#com.watchrx.watchrxhealth.pedometer.SensorListener
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:386:9-388:40
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:388:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:387:13-53
service#com.watchrx.watchrxhealth.WatchRxForegroundService
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:389:9-393:74
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:391:13-37
	android:permission
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:393:13-71
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:392:13-58
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:390:13-53
service#com.zipow.videobox.share.ScreenShareServiceForSDK
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:394:9-400:61
	android:label
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:398:13-33
	android:exported
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:396:13-37
	tools:ignore
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:399:13-55
	tools:replace
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:400:13-58
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:397:13-71
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:395:13-77
uses-library#org.apache.http.legacy
ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:402:9-404:40
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:404:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:403:13-50
uses-sdk
INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml
MERGED from [com.github.GoodieBag:Pinview:v1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b86d17340e149f32b42d8e4b4155161e\transformed\jetified-Pinview-v1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.GoodieBag:Pinview:v1.4] C:\Users\<USER>\.gradle\caches\transforms-3\b86d17340e149f32b42d8e4b4155161e\transformed\jetified-Pinview-v1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee139caef85bfed62fe92dd3a70d103a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee139caef85bfed62fe92dd3a70d103a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.applandeo:material-calendar-view:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-3\c94defda84e4aea81c759dab29875d1f\transformed\jetified-material-calendar-view-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [com.applandeo:material-calendar-view:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-3\c94defda84e4aea81c759dab29875d1f\transformed\jetified-material-calendar-view-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f533fcbc46a57fb9ff7758295e66825\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0f533fcbc46a57fb9ff7758295e66825\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\98720b29c7f34fbfef5d30a8961d4610\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\98720b29c7f34fbfef5d30a8961d4610\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:5:5-44
MERGED from [us.zoom.uitoolkit:uitoolkit:1.11.2-1] C:\Users\<USER>\.gradle\caches\transforms-3\5057ec80a63cbdf1b3c7fa6aaf5d6a71\transformed\jetified-uitoolkit-1.11.2-1\AndroidManifest.xml:5:5-44
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:5:5-7:41
MERGED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\323bed96daa27a02359b2078b79c05c6\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\323bed96daa27a02359b2078b79c05c6\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\42fcd71a3362c3d3c804d610cf3f2c72\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\42fcd71a3362c3d3c804d610cf3f2c72\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5bf5e5c6ef9e8b277937fda46aabee7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5bf5e5c6ef9e8b277937fda46aabee7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaf23df0ef1ecaa7e75b8d117c11c175\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaf23df0ef1ecaa7e75b8d117c11c175\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\497163c371a489f5abc521cbbdd03f6a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\497163c371a489f5abc521cbbdd03f6a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16898ebf9e4c90c191a5cd0a61417114\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\16898ebf9e4c90c191a5cd0a61417114\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e2e54c84504d4b5d4bbcd5b5a658108\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e2e54c84504d4b5d4bbcd5b5a658108\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:21:5-44
MERGED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\acfcc9326a00e293517549780da1e94b\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\acfcc9326a00e293517549780da1e94b\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\182ddcdfdcce3e4ca41e0673f1f70b41\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\182ddcdfdcce3e4ca41e0673f1f70b41\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c33b659fd63b325502dfc0b524f9bdc4\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c33b659fd63b325502dfc0b524f9bdc4\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c742509490d53adfb7875f5ea1fbf\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a8c742509490d53adfb7875f5ea1fbf\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03e0667da5aa10808c0b497f6ef59386\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\03e0667da5aa10808c0b497f6ef59386\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\57c205b2a69818851dbd59e42765446d\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\57c205b2a69818851dbd59e42765446d\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f12f4461742a793f75d02b9f893a8a05\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f12f4461742a793f75d02b9f893a8a05\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.work:work-runtime-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\2da4482590d8aff2abab2cb29a9498f8\transformed\work-runtime-ktx-2.7.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\2da4482590d8aff2abab2cb29a9498f8\transformed\work-runtime-ktx-2.7.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.gridlayout:gridlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc65449114092406ba100a0c99207fb8\transformed\gridlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.gridlayout:gridlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc65449114092406ba100a0c99207fb8\transformed\gridlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3e224de2962769b02daf67946df0276\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3e224de2962769b02daf67946df0276\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\357ca665e17609b0c4b22bac231a4d55\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\357ca665e17609b0c4b22bac231a4d55\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\50f4bdfa866f097bcb5f1b8e94eac5ee\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\50f4bdfa866f097bcb5f1b8e94eac5ee\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\975134eed933950f502dc0138ce9ae15\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\975134eed933950f502dc0138ce9ae15\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\7b45cd5be285aa2c7bb9d6f23bd2e316\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\7b45cd5be285aa2c7bb9d6f23bd2e316\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\116096bb688bc1e303194276f340877f\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\116096bb688bc1e303194276f340877f\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5298376b6a3b07f76e39da2b00fe6abc\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5298376b6a3b07f76e39da2b00fe6abc\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c496671a68b8e9628512bef2587c7af2\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c496671a68b8e9628512bef2587c7af2\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a77c5ae137c73c8a2517bce2f0159341\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a77c5ae137c73c8a2517bce2f0159341\transformed\jetified-lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\00b602cc22e24b38e469887e456c028e\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\00b602cc22e24b38e469887e456c028e\transformed\jetified-lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e9cc04b6b1c9480909f53dcb2c61f88\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e9cc04b6b1c9480909f53dcb2c61f88\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29611d231b6306739a8efd62e51a3795\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29611d231b6306739a8efd62e51a3795\transformed\jetified-lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\752d9797f158c0fdb0f3caeb19a14651\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\752d9797f158c0fdb0f3caeb19a14651\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeaefd5da97e94ec3fc3cd55867cde2e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\aeaefd5da97e94ec3fc3cd55867cde2e\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bab69c72513635a63b5299e261d854f\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8bab69c72513635a63b5299e261d854f\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\06aa9dc8f505a27c5233ca7991a98d55\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\06aa9dc8f505a27c5233ca7991a98d55\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd989bbca9253df37428ca87de178143\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd989bbca9253df37428ca87de178143\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\76cb69c2fe39df612498a1c977b35b87\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\76cb69c2fe39df612498a1c977b35b87\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ace4240242d7e30916f29befa3ff2d25\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ace4240242d7e30916f29befa3ff2d25\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\e81b820efb681ef4f53988c1afa8d787\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\transforms-3\e81b820efb681ef4f53988c1afa8d787\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfc6b2dc1b1e8d7695a962a47974e93a\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfc6b2dc1b1e8d7695a962a47974e93a\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b0edbd2979ae96216d8e5e558dae44e\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b0edbd2979ae96216d8e5e558dae44e\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\267985dc04d40e215387f1f013ce0e5f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\267985dc04d40e215387f1f013ce0e5f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\965e73c60b960f9618dcd798b95c371f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\965e73c60b960f9618dcd798b95c371f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\39daceb33ba91659cedff614c22a6e5c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\39daceb33ba91659cedff614c22a6e5c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6450e1e5d34d387e191f2f0f9c5324df\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6450e1e5d34d387e191f2f0f9c5324df\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4119d5db31bc69bce75ff5007ce4ae7\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4119d5db31bc69bce75ff5007ce4ae7\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\951b9fed77767cfb0d7e80606a592bbf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\951b9fed77767cfb0d7e80606a592bbf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7396de89cd5cec42bf887812134173\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e7396de89cd5cec42bf887812134173\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e321fe3f4c17167c92ba96d58d370984\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e321fe3f4c17167c92ba96d58d370984\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\edb8a9842af09e8ef63a8283d4e57e4a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\edb8a9842af09e8ef63a8283d4e57e4a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\323e1c48885638197f1bc4278940a379\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\323e1c48885638197f1bc4278940a379\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\7ed42c12cb279249327ac70e76bb72eb\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\7ed42c12cb279249327ac70e76bb72eb\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eefc612161d90b6bd0031304794c24b9\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eefc612161d90b6bd0031304794c24b9\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05513f78504591973ae7f1848436c86a\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [org.greenrobot:eventbus:3.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05513f78504591973ae7f1848436c86a\transformed\jetified-eventbus-3.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.twilio:audioswitch:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\903a21fdcfea6838d8c1375fe6560388\transformed\jetified-audioswitch-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.twilio:audioswitch:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\903a21fdcfea6838d8c1375fe6560388\transformed\jetified-audioswitch-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.twilio:video-android:7.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\ed01b5b8f9cea297e728a0087469107c\transformed\jetified-video-android-7.0.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.twilio:voice-android:6.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\169320f0c2abb260a916a47369a236b4\transformed\jetified-voice-android-6.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d574bc0f4bbe001d4fafcf10e48cd68c\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d574bc0f4bbe001d4fafcf10e48cd68c\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c70b5acd25a6a846e95150d61b579a9d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c70b5acd25a6a846e95150d61b579a9d\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\805004bfd7065da2889542de939ea64d\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\805004bfd7065da2889542de939ea64d\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\390f4f6c902a60bf590fca236a0f2505\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\390f4f6c902a60bf590fca236a0f2505\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ede0399808c17d9b7f9da6bb0e818d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ede0399808c17d9b7f9da6bb0e818d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75d3b07ab426e8f0c2820d7b9009f233\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\75d3b07ab426e8f0c2820d7b9009f233\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a8ac7cea3fe20e30fac01c2af47cdcb\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a8ac7cea3fe20e30fac01c2af47cdcb\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa8517b5200bb3d0892b6a6801022804\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa8517b5200bb3d0892b6a6801022804\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-3\7c08fbcf380424f7a04ebeb3b8f331a4\transformed\jetified-security-crypto-1.1.0-alpha03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-3\7c08fbcf380424f7a04ebeb3b8f331a4\transformed\jetified-security-crypto-1.1.0-alpha03\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f87b76aeadea9247567ecb3d965f4355\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f87b76aeadea9247567ecb3d965f4355\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5c40d3058318171a542817b0be734bf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5c40d3058318171a542817b0be734bf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac93541a53afe9cc247064049ea1b347\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac93541a53afe9cc247064049ea1b347\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e9c34f910da9af6bf1f69f00c116090\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e9c34f910da9af6bf1f69f00c116090\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f68016607b3392c6493a8cc16928621c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f68016607b3392c6493a8cc16928621c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cf342c09250091525de2fd613e4977e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\0cf342c09250091525de2fd613e4977e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4133846e7f2e2644ba88403abc9ba3ee\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4133846e7f2e2644ba88403abc9ba3ee\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df9fb09667536424562f03385f007d84\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df9fb09667536424562f03385f007d84\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\bbd7d3144b85d65325b6aea7a855990b\transformed\jetified-relinker-1.4.4\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\bbd7d3144b85d65325b6aea7a855990b\transformed\jetified-relinker-1.4.4\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml
uses-feature#android.hardware.telephony
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:16:5-18:36
	android:required
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:18:9-33
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:17:9-50
uses-feature#android.hardware.bluetooth
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:19:5-21:36
	android:required
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:21:9-33
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:20:9-50
uses-feature#android.hardware.microphone
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:22:5-24:36
	android:required
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:24:9-33
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:23:9-51
uses-permission#android.permission.BROADCAST_STICKY
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:44:5-75
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:44:22-72
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:50:5-78
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:50:22-75
uses-permission#android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:55:5-94
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:55:22-91
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:5-88
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:22-85
uses-permission#android.permission.FOREGROUND_SERVICE_PHONE_CALL
ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:57:5-88
	android:name
		ADDED from [us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:57:22-85
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2808aaa93e11aab77d556509cd3833c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:13-52:29
	android:priority
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:28-51
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
receiver#com.google.firebase.messaging.directboot.FirebaseMessagingDirectBootReceiver
ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:32:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:35:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:36:13-73
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:33:13-104
intent-filter#action:name:com.google.firebase.messaging.RECEIVE_DIRECT_BOOT
ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:37:13-39:29
action#com.google.firebase.messaging.RECEIVE_DIRECT_BOOT
ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:38:17-92
	android:name
		ADDED from [com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:38:25-89
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
service#androidx.health.platform.client.impl.sdkservice.HealthDataSdkService
ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:27:13-36
	tools:ignore
		ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:28:13-43
	android:name
		ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:25:13-96
intent-filter#action:name:androidx.health.platform.client.ACTION_BIND_SDK_SERVICE
ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:29:13-31:29
action#androidx.health.platform.client.ACTION_BIND_SDK_SERVICE
ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:30:17-98
	android:name
		ADDED from [androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:30:25-95
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ede0399808c17d9b7f9da6bb0e818d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ede0399808c17d9b7f9da6bb0e818d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
