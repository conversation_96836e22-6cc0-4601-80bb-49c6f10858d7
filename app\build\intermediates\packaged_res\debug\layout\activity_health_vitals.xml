<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:padding="16dp">

    <TextView
        android:id="@+id/title_health_vitals"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/lato_bold"
        android:text="@string/health_vitals"
        android:textColor="#000000"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/subtitle_health_connect"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/lato_regular"
        android:text="@string/health_connect_data"
        android:textColor="#666666"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_health_vitals" />

    <Button
        android:id="@+id/btn_grant_permissions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_shape_green"
        android:fontFamily="@font/lato_bold"
        android:text="@string/enable_health_connect"
        android:textColor="@android:color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/subtitle_health_connect" />

    <ScrollView
        android:id="@+id/scroll_health_data"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_grant_permissions">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Steps Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_steps"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/bg_vitals_icon">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_baseline_directions_walk_24"
                            app:tint="@android:color/white" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/lato_bold"
                            android:text="@string/steps_today"
                            android:textColor="#000000"
                            android:textSize="18sp" />

                        <TextView
                            android:id="@+id/tv_steps_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="@font/lato_regular"
                            android:text="--"
                            android:textColor="#666666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_steps_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/lato_bold"
                        android:text="0"
                        android:textColor="@color/purple_700"
                        android:textSize="28sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Heart Rate Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_heart_rate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/bg_vitals_icon">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_baseline_favorite_24"
                            app:tint="@android:color/white" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/lato_bold"
                            android:text="@string/heart_rate"
                            android:textColor="#000000"
                            android:textSize="18sp" />

                        <TextView
                            android:id="@+id/tv_heart_rate_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="@font/lato_regular"
                            android:text="--"
                            android:textColor="#666666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_heart_rate_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/lato_bold"
                        android:text="--"
                        android:textColor="@color/purple_700"
                        android:textSize="28sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Calories Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_calories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/bg_vitals_icon">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_calorie"
                            app:tint="@android:color/white" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/lato_bold"
                            android:text="@string/calories_burned"
                            android:textColor="#000000"
                            android:textSize="18sp" />

                        <TextView
                            android:id="@+id/tv_calories_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="@font/lato_regular"
                            android:text="Today"
                            android:textColor="#666666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_calories_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/lato_bold"
                        android:text="0"
                        android:textColor="@color/purple_700"
                        android:textSize="28sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Sleep Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_sleep"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <FrameLayout
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/bg_vitals_icon">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:src="@drawable/sleep"
                            app:tint="@android:color/white" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/lato_bold"
                            android:text="@string/sleep_hours"
                            android:textColor="#000000"
                            android:textSize="18sp" />

                        <TextView
                            android:id="@+id/tv_sleep_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="@font/lato_regular"
                            android:text="Last night"
                            android:textColor="#666666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_sleep_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/lato_bold"
                        android:text="--"
                        android:textColor="@color/purple_700"
                        android:textSize="28sp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- No Data Message -->
            <TextView
                android:id="@+id/tv_no_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:fontFamily="@font/lato_regular"
                android:gravity="center"
                android:text="@string/no_health_data"
                android:textColor="#999999"
                android:textSize="16sp"
                android:visibility="gone" />

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>