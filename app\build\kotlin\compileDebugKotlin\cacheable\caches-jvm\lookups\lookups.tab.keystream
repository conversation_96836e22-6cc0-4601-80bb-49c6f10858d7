  	Exception android.app.Activity  HealthConnectManager android.app.Activity  LogUtils android.app.Activity  R android.app.Activity  Set android.app.Activity  String android.app.Activity  Toast android.app.Activity  View android.app.Activity  btnGrantPermissions android.app.Activity  format android.app.Activity  formatStepsCount android.app.Activity  healthConnectManager android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  
loadStepsData android.app.Activity  onCreate android.app.Activity  replace android.app.Activity  showErrorMessage android.app.Activity  "showSampleDataWithPermissionPrompt android.app.Activity  toRegex android.app.Activity  tvCaloriesTime android.app.Activity  tvCaloriesValue android.app.Activity  tvHeartRateTime android.app.Activity  tvHeartRateValue android.app.Activity  tvNoData android.app.Activity  tvSleepTime android.app.Activity  tvSleepValue android.app.Activity  tvStepsCount android.app.Activity  tvStepsValue android.app.Activity  Context android.content  	Exception android.content.Context  HealthConnectManager android.content.Context  LogUtils android.content.Context  R android.content.Context  Set android.content.Context  String android.content.Context  Toast android.content.Context  View android.content.Context  btnGrantPermissions android.content.Context  format android.content.Context  formatStepsCount android.content.Context  healthConnectManager android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  
loadStepsData android.content.Context  replace android.content.Context  showErrorMessage android.content.Context  "showSampleDataWithPermissionPrompt android.content.Context  toRegex android.content.Context  tvCaloriesTime android.content.Context  tvCaloriesValue android.content.Context  tvHeartRateTime android.content.Context  tvHeartRateValue android.content.Context  tvNoData android.content.Context  tvSleepTime android.content.Context  tvSleepValue android.content.Context  tvStepsCount android.content.Context  tvStepsValue android.content.Context  	Exception android.content.ContextWrapper  HealthConnectManager android.content.ContextWrapper  LogUtils android.content.ContextWrapper  R android.content.ContextWrapper  Set android.content.ContextWrapper  String android.content.ContextWrapper  Toast android.content.ContextWrapper  View android.content.ContextWrapper  btnGrantPermissions android.content.ContextWrapper  format android.content.ContextWrapper  formatStepsCount android.content.ContextWrapper  healthConnectManager android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  
loadStepsData android.content.ContextWrapper  replace android.content.ContextWrapper  showErrorMessage android.content.ContextWrapper  "showSampleDataWithPermissionPrompt android.content.ContextWrapper  toRegex android.content.ContextWrapper  tvCaloriesTime android.content.ContextWrapper  tvCaloriesValue android.content.ContextWrapper  tvHeartRateTime android.content.ContextWrapper  tvHeartRateValue android.content.ContextWrapper  tvNoData android.content.ContextWrapper  tvSleepTime android.content.ContextWrapper  tvSleepValue android.content.ContextWrapper  tvStepsCount android.content.ContextWrapper  tvStepsValue android.content.ContextWrapper  Bitmap android.graphics  
BitmapFactory android.graphics  ImageDecoder android.graphics  ImageFormat android.graphics  Matrix android.graphics  Rect android.graphics  YuvImage android.graphics  createBitmap android.graphics.Bitmap  height android.graphics.Bitmap  width android.graphics.Bitmap  decodeByteArray android.graphics.BitmapFactory  Source android.graphics.ImageDecoder  createSource android.graphics.ImageDecoder  decodeBitmap android.graphics.ImageDecoder  NV21 android.graphics.ImageFormat  
postRotate android.graphics.Matrix  compressToJpeg android.graphics.YuvImage  height android.graphics.YuvImage  width android.graphics.YuvImage  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  Log android.util  e android.util.Log  View android.view  	Exception  android.view.ContextThemeWrapper  HealthConnectManager  android.view.ContextThemeWrapper  LogUtils  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  Set  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  btnGrantPermissions  android.view.ContextThemeWrapper  format  android.view.ContextThemeWrapper  formatStepsCount  android.view.ContextThemeWrapper  healthConnectManager  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  
loadStepsData  android.view.ContextThemeWrapper  replace  android.view.ContextThemeWrapper  showErrorMessage  android.view.ContextThemeWrapper  "showSampleDataWithPermissionPrompt  android.view.ContextThemeWrapper  toRegex  android.view.ContextThemeWrapper  tvCaloriesTime  android.view.ContextThemeWrapper  tvCaloriesValue  android.view.ContextThemeWrapper  tvHeartRateTime  android.view.ContextThemeWrapper  tvHeartRateValue  android.view.ContextThemeWrapper  tvNoData  android.view.ContextThemeWrapper  tvSleepTime  android.view.ContextThemeWrapper  tvSleepValue  android.view.ContextThemeWrapper  tvStepsCount  android.view.ContextThemeWrapper  tvStepsValue  android.view.ContextThemeWrapper  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  Button android.widget  TextView android.widget  Toast android.widget  setOnClickListener android.widget.Button  
visibility android.widget.Button  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  	Exception #androidx.activity.ComponentActivity  HealthConnectManager #androidx.activity.ComponentActivity  LogUtils #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  Set #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  btnGrantPermissions #androidx.activity.ComponentActivity  format #androidx.activity.ComponentActivity  formatStepsCount #androidx.activity.ComponentActivity  healthConnectManager #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  
loadStepsData #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  replace #androidx.activity.ComponentActivity  showErrorMessage #androidx.activity.ComponentActivity  "showSampleDataWithPermissionPrompt #androidx.activity.ComponentActivity  toRegex #androidx.activity.ComponentActivity  tvCaloriesTime #androidx.activity.ComponentActivity  tvCaloriesValue #androidx.activity.ComponentActivity  tvHeartRateTime #androidx.activity.ComponentActivity  tvHeartRateValue #androidx.activity.ComponentActivity  tvNoData #androidx.activity.ComponentActivity  tvSleepTime #androidx.activity.ComponentActivity  tvSleepValue #androidx.activity.ComponentActivity  tvStepsCount #androidx.activity.ComponentActivity  tvStepsValue #androidx.activity.ComponentActivity  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContract !androidx.activity.result.contract  AppCompatActivity androidx.appcompat.app  	Exception (androidx.appcompat.app.AppCompatActivity  HealthConnectManager (androidx.appcompat.app.AppCompatActivity  LogUtils (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  Set (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  btnGrantPermissions (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  format (androidx.appcompat.app.AppCompatActivity  formatStepsCount (androidx.appcompat.app.AppCompatActivity  healthConnectManager (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  
loadStepsData (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  replace (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  showErrorMessage (androidx.appcompat.app.AppCompatActivity  "showSampleDataWithPermissionPrompt (androidx.appcompat.app.AppCompatActivity  toRegex (androidx.appcompat.app.AppCompatActivity  tvCaloriesTime (androidx.appcompat.app.AppCompatActivity  tvCaloriesValue (androidx.appcompat.app.AppCompatActivity  tvHeartRateTime (androidx.appcompat.app.AppCompatActivity  tvHeartRateValue (androidx.appcompat.app.AppCompatActivity  tvNoData (androidx.appcompat.app.AppCompatActivity  tvSleepTime (androidx.appcompat.app.AppCompatActivity  tvSleepValue (androidx.appcompat.app.AppCompatActivity  tvStepsCount (androidx.appcompat.app.AppCompatActivity  tvStepsValue (androidx.appcompat.app.AppCompatActivity  	Exception #androidx.core.app.ComponentActivity  HealthConnectManager #androidx.core.app.ComponentActivity  LogUtils #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  Set #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  btnGrantPermissions #androidx.core.app.ComponentActivity  format #androidx.core.app.ComponentActivity  formatStepsCount #androidx.core.app.ComponentActivity  healthConnectManager #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  
loadStepsData #androidx.core.app.ComponentActivity  replace #androidx.core.app.ComponentActivity  showErrorMessage #androidx.core.app.ComponentActivity  "showSampleDataWithPermissionPrompt #androidx.core.app.ComponentActivity  toRegex #androidx.core.app.ComponentActivity  tvCaloriesTime #androidx.core.app.ComponentActivity  tvCaloriesValue #androidx.core.app.ComponentActivity  tvHeartRateTime #androidx.core.app.ComponentActivity  tvHeartRateValue #androidx.core.app.ComponentActivity  tvNoData #androidx.core.app.ComponentActivity  tvSleepTime #androidx.core.app.ComponentActivity  tvSleepValue #androidx.core.app.ComponentActivity  tvStepsCount #androidx.core.app.ComponentActivity  tvStepsValue #androidx.core.app.ComponentActivity  	Exception &androidx.fragment.app.FragmentActivity  HealthConnectManager &androidx.fragment.app.FragmentActivity  LogUtils &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  Set &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  btnGrantPermissions &androidx.fragment.app.FragmentActivity  format &androidx.fragment.app.FragmentActivity  formatStepsCount &androidx.fragment.app.FragmentActivity  healthConnectManager &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  
loadStepsData &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  replace &androidx.fragment.app.FragmentActivity  showErrorMessage &androidx.fragment.app.FragmentActivity  "showSampleDataWithPermissionPrompt &androidx.fragment.app.FragmentActivity  toRegex &androidx.fragment.app.FragmentActivity  tvCaloriesTime &androidx.fragment.app.FragmentActivity  tvCaloriesValue &androidx.fragment.app.FragmentActivity  tvHeartRateTime &androidx.fragment.app.FragmentActivity  tvHeartRateValue &androidx.fragment.app.FragmentActivity  tvNoData &androidx.fragment.app.FragmentActivity  tvSleepTime &androidx.fragment.app.FragmentActivity  tvSleepValue &androidx.fragment.app.FragmentActivity  tvStepsCount &androidx.fragment.app.FragmentActivity  tvStepsValue &androidx.fragment.app.FragmentActivity  HealthConnectClient androidx.health.connect.client  PermissionController androidx.health.connect.client  	Companion 2androidx.health.connect.client.HealthConnectClient  SDK_UNAVAILABLE 2androidx.health.connect.client.HealthConnectClient  (SDK_UNAVAILABLE_PROVIDER_UPDATE_REQUIRED 2androidx.health.connect.client.HealthConnectClient  	aggregate 2androidx.health.connect.client.HealthConnectClient  getOrCreate 2androidx.health.connect.client.HealthConnectClient  getSdkStatus 2androidx.health.connect.client.HealthConnectClient  
insertRecords 2androidx.health.connect.client.HealthConnectClient  permissionController 2androidx.health.connect.client.HealthConnectClient  readRecords 2androidx.health.connect.client.HealthConnectClient  SDK_UNAVAILABLE <androidx.health.connect.client.HealthConnectClient.Companion  (SDK_UNAVAILABLE_PROVIDER_UPDATE_REQUIRED <androidx.health.connect.client.HealthConnectClient.Companion  getOrCreate <androidx.health.connect.client.HealthConnectClient.Companion  getSdkStatus <androidx.health.connect.client.HealthConnectClient.Companion  	Companion 3androidx.health.connect.client.PermissionController  %createRequestPermissionResultContract 3androidx.health.connect.client.PermissionController  getGrantedPermissions 3androidx.health.connect.client.PermissionController  %createRequestPermissionResultContract =androidx.health.connect.client.PermissionController.Companion  AggregateMetric (androidx.health.connect.client.aggregate  AggregationResult (androidx.health.connect.client.aggregate  get :androidx.health.connect.client.aggregate.AggregationResult  HealthPermission )androidx.health.connect.client.permission  	Companion :androidx.health.connect.client.permission.HealthPermission  getReadPermission :androidx.health.connect.client.permission.HealthPermission  getWritePermission :androidx.health.connect.client.permission.HealthPermission  getReadPermission Dandroidx.health.connect.client.permission.HealthPermission.Companion  getWritePermission Dandroidx.health.connect.client.permission.HealthPermission.Companion  StepsRecord &androidx.health.connect.client.records  COUNT_TOTAL 2androidx.health.connect.client.records.StepsRecord  	Companion 2androidx.health.connect.client.records.StepsRecord  count 2androidx.health.connect.client.records.StepsRecord  endTime 2androidx.health.connect.client.records.StepsRecord  	startTime 2androidx.health.connect.client.records.StepsRecord  COUNT_TOTAL <androidx.health.connect.client.records.StepsRecord.Companion  AggregateRequest &androidx.health.connect.client.request  ReadRecordsRequest &androidx.health.connect.client.request  InsertRecordsResponse 'androidx.health.connect.client.response  ReadRecordsResponse 'androidx.health.connect.client.response  records ;androidx.health.connect.client.response.ReadRecordsResponse  TimeRangeFilter #androidx.health.connect.client.time  	Companion 3androidx.health.connect.client.time.TimeRangeFilter  between 3androidx.health.connect.client.time.TimeRangeFilter  between =androidx.health.connect.client.time.TimeRangeFilter.Companion  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  Array  com.twilio.video.examples.common  Bitmap  com.twilio.video.examples.common  
BitmapFactory  com.twilio.video.examples.common  Build  com.twilio.video.examples.common  	ByteArray  com.twilio.video.examples.common  ByteArrayOutputStream  com.twilio.video.examples.common  
ByteBuffer  com.twilio.video.examples.common  IOException  com.twilio.video.examples.common  ImageDecoder  com.twilio.video.examples.common  ImageFormat  com.twilio.video.examples.common  Int  com.twilio.video.examples.common  IntArray  com.twilio.video.examples.common  Matrix  com.twilio.video.examples.common  Rect  com.twilio.video.examples.common  
VideoFrame  com.twilio.video.examples.common  YuvConverter  com.twilio.video.examples.common  YuvImage  com.twilio.video.examples.common  arrayOf  com.twilio.video.examples.common  	copyPlane  com.twilio.video.examples.common  fastI420ToYuvImage  com.twilio.video.examples.common  i420ToYuvImage  com.twilio.video.examples.common  
intArrayOf  com.twilio.video.examples.common  toBitmap  com.twilio.video.examples.common  until  com.twilio.video.examples.common  
I420Buffer +com.twilio.video.examples.common.VideoFrame  
TextureBuffer +com.twilio.video.examples.common.VideoFrame  ActivityResultLauncher com.watchrx.watchrxhealth  AppCompatActivity com.watchrx.watchrxhealth  Bundle com.watchrx.watchrxhealth  Button com.watchrx.watchrxhealth  	Exception com.watchrx.watchrxhealth  HealthConnectManager com.watchrx.watchrxhealth  HealthVitalsActivity com.watchrx.watchrxhealth  LogUtils com.watchrx.watchrxhealth  Long com.watchrx.watchrxhealth  R com.watchrx.watchrxhealth  Set com.watchrx.watchrxhealth  String com.watchrx.watchrxhealth  TextView com.watchrx.watchrxhealth  Toast com.watchrx.watchrxhealth  View com.watchrx.watchrxhealth  btnGrantPermissions com.watchrx.watchrxhealth  format com.watchrx.watchrxhealth  formatStepsCount com.watchrx.watchrxhealth  healthConnectManager com.watchrx.watchrxhealth  launch com.watchrx.watchrxhealth  
loadStepsData com.watchrx.watchrxhealth  replace com.watchrx.watchrxhealth  showErrorMessage com.watchrx.watchrxhealth  "showSampleDataWithPermissionPrompt com.watchrx.watchrxhealth  toRegex com.watchrx.watchrxhealth  tvCaloriesTime com.watchrx.watchrxhealth  tvCaloriesValue com.watchrx.watchrxhealth  tvHeartRateTime com.watchrx.watchrxhealth  tvHeartRateValue com.watchrx.watchrxhealth  tvNoData com.watchrx.watchrxhealth  tvSleepTime com.watchrx.watchrxhealth  tvSleepValue com.watchrx.watchrxhealth  tvStepsCount com.watchrx.watchrxhealth  tvStepsValue com.watchrx.watchrxhealth  ActivityResultLauncher .com.watchrx.watchrxhealth.HealthVitalsActivity  Bundle .com.watchrx.watchrxhealth.HealthVitalsActivity  Button .com.watchrx.watchrxhealth.HealthVitalsActivity  	Exception .com.watchrx.watchrxhealth.HealthVitalsActivity  HealthConnectManager .com.watchrx.watchrxhealth.HealthVitalsActivity  LogUtils .com.watchrx.watchrxhealth.HealthVitalsActivity  Long .com.watchrx.watchrxhealth.HealthVitalsActivity  R .com.watchrx.watchrxhealth.HealthVitalsActivity  Set .com.watchrx.watchrxhealth.HealthVitalsActivity  String .com.watchrx.watchrxhealth.HealthVitalsActivity  TextView .com.watchrx.watchrxhealth.HealthVitalsActivity  Toast .com.watchrx.watchrxhealth.HealthVitalsActivity  View .com.watchrx.watchrxhealth.HealthVitalsActivity  btnGrantPermissions .com.watchrx.watchrxhealth.HealthVitalsActivity  checkHealthConnectAndLoadData .com.watchrx.watchrxhealth.HealthVitalsActivity  findViewById .com.watchrx.watchrxhealth.HealthVitalsActivity  format .com.watchrx.watchrxhealth.HealthVitalsActivity  formatStepsCount .com.watchrx.watchrxhealth.HealthVitalsActivity  healthConnectManager .com.watchrx.watchrxhealth.HealthVitalsActivity  hideAllDataCards .com.watchrx.watchrxhealth.HealthVitalsActivity  initializeHealthConnect .com.watchrx.watchrxhealth.HealthVitalsActivity  initializeViews .com.watchrx.watchrxhealth.HealthVitalsActivity  launch .com.watchrx.watchrxhealth.HealthVitalsActivity  lifecycleScope .com.watchrx.watchrxhealth.HealthVitalsActivity  
loadStepsData .com.watchrx.watchrxhealth.HealthVitalsActivity  permissionLauncher .com.watchrx.watchrxhealth.HealthVitalsActivity  registerForActivityResult .com.watchrx.watchrxhealth.HealthVitalsActivity  replace .com.watchrx.watchrxhealth.HealthVitalsActivity  requestHealthConnectPermissions .com.watchrx.watchrxhealth.HealthVitalsActivity  setContentView .com.watchrx.watchrxhealth.HealthVitalsActivity  setupPermissionLauncher .com.watchrx.watchrxhealth.HealthVitalsActivity  showErrorMessage .com.watchrx.watchrxhealth.HealthVitalsActivity  showNeedsUpdateMessage .com.watchrx.watchrxhealth.HealthVitalsActivity  showNotSupportedMessage .com.watchrx.watchrxhealth.HealthVitalsActivity  "showSampleDataWithPermissionPrompt .com.watchrx.watchrxhealth.HealthVitalsActivity  toRegex .com.watchrx.watchrxhealth.HealthVitalsActivity  tvCaloriesTime .com.watchrx.watchrxhealth.HealthVitalsActivity  tvCaloriesValue .com.watchrx.watchrxhealth.HealthVitalsActivity  tvHeartRateTime .com.watchrx.watchrxhealth.HealthVitalsActivity  tvHeartRateValue .com.watchrx.watchrxhealth.HealthVitalsActivity  tvNoData .com.watchrx.watchrxhealth.HealthVitalsActivity  tvSleepTime .com.watchrx.watchrxhealth.HealthVitalsActivity  tvSleepValue .com.watchrx.watchrxhealth.HealthVitalsActivity  tvStepsCount .com.watchrx.watchrxhealth.HealthVitalsActivity  tvStepsValue .com.watchrx.watchrxhealth.HealthVitalsActivity  HealthConnectManager 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  LogUtils 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  R 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  String 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  Toast 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  View 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  btnGrantPermissions 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  format 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  formatStepsCount 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  healthConnectManager 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  launch 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  lifecycleScope 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  
loadStepsData 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  replace 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  showErrorMessage 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  "showSampleDataWithPermissionPrompt 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  toRegex 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvCaloriesTime 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvCaloriesValue 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvHeartRateTime 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvHeartRateValue 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvNoData 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvSleepTime 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvSleepValue 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvStepsCount 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  tvStepsValue 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  btn_grant_permissions com.watchrx.watchrxhealth.R.id  tv_calories_time com.watchrx.watchrxhealth.R.id  tv_calories_value com.watchrx.watchrxhealth.R.id  tv_heart_rate_time com.watchrx.watchrxhealth.R.id  tv_heart_rate_value com.watchrx.watchrxhealth.R.id  
tv_no_data com.watchrx.watchrxhealth.R.id  
tv_sleep_time com.watchrx.watchrxhealth.R.id  tv_sleep_value com.watchrx.watchrxhealth.R.id  tv_steps_count com.watchrx.watchrxhealth.R.id  tv_steps_value com.watchrx.watchrxhealth.R.id  activity_health_vitals "com.watchrx.watchrxhealth.R.layout  AggregateRequest 'com.watchrx.watchrxhealth.healthconnect  Boolean 'com.watchrx.watchrxhealth.healthconnect  Context 'com.watchrx.watchrxhealth.healthconnect  Dispatchers 'com.watchrx.watchrxhealth.healthconnect  	Exception 'com.watchrx.watchrxhealth.healthconnect  HEALTH_CONNECT_PACKAGE_NAME 'com.watchrx.watchrxhealth.healthconnect  HealthConnectAvailability 'com.watchrx.watchrxhealth.healthconnect  HealthConnectClient 'com.watchrx.watchrxhealth.healthconnect  HealthConnectManager 'com.watchrx.watchrxhealth.healthconnect  HealthPermission 'com.watchrx.watchrxhealth.healthconnect  Instant 'com.watchrx.watchrxhealth.healthconnect  List 'com.watchrx.watchrxhealth.healthconnect  	LocalDate 'com.watchrx.watchrxhealth.healthconnect  Log 'com.watchrx.watchrxhealth.healthconnect  LogUtils 'com.watchrx.watchrxhealth.healthconnect  Long 'com.watchrx.watchrxhealth.healthconnect  PermissionController 'com.watchrx.watchrxhealth.healthconnect  ReadRecordsRequest 'com.watchrx.watchrxhealth.healthconnect  Set 'com.watchrx.watchrxhealth.healthconnect  StepData 'com.watchrx.watchrxhealth.healthconnect  StepsRecord 'com.watchrx.watchrxhealth.healthconnect  String 'com.watchrx.watchrxhealth.healthconnect  TAG 'com.watchrx.watchrxhealth.healthconnect  TimeRangeFilter 'com.watchrx.watchrxhealth.healthconnect  ZoneId 'com.watchrx.watchrxhealth.healthconnect  
ZoneOffset 'com.watchrx.watchrxhealth.healthconnect  between 'com.watchrx.watchrxhealth.healthconnect  %createRequestPermissionResultContract 'com.watchrx.watchrxhealth.healthconnect  	emptyList 'com.watchrx.watchrxhealth.healthconnect  getHealthConnectClient 'com.watchrx.watchrxhealth.healthconnect  getOrCreate 'com.watchrx.watchrxhealth.healthconnect  getReadPermission 'com.watchrx.watchrxhealth.healthconnect  getSdkStatus 'com.watchrx.watchrxhealth.healthconnect  getStepsPermissions 'com.watchrx.watchrxhealth.healthconnect  getWritePermission 'com.watchrx.watchrxhealth.healthconnect  listOf 'com.watchrx.watchrxhealth.healthconnect  map 'com.watchrx.watchrxhealth.healthconnect  setOf 'com.watchrx.watchrxhealth.healthconnect  withContext 'com.watchrx.watchrxhealth.healthconnect  AggregateRequest <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  Boolean <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  	Companion <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  Context <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  Dispatchers <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  	Exception <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  HEALTH_CONNECT_PACKAGE_NAME <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  HealthConnectAvailability <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  HealthConnectClient <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  HealthPermission <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  Instant <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  List <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  	LocalDate <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  Log <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  LogUtils <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  Long <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  PermissionController <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  ReadRecordsRequest <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  Set <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  StepData <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  StepsRecord <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  String <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  TAG <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  TimeRangeFilter <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  ZoneId <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  
ZoneOffset <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  between <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  checkHealthConnectAvailability <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  context <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  createPermissionRequestContract <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  %createRequestPermissionResultContract <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  	emptyList <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  getHealthConnectClient <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  getOrCreate <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  getReadPermission <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  getSdkStatus <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  getStepsPermissions <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  
getTodaySteps <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  getWritePermission <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  hasStepsPermissions <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  healthConnectClient <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  listOf <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  map <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  setOf <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  withContext <com.watchrx.watchrxhealth.healthconnect.HealthConnectManager  AggregateRequest Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  Dispatchers Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  HEALTH_CONNECT_PACKAGE_NAME Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  HealthConnectAvailability Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  HealthConnectClient Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  HealthPermission Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  	LocalDate Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  Log Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  LogUtils Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  PermissionController Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  ReadRecordsRequest Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  StepData Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  StepsRecord Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  TAG Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  TimeRangeFilter Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  ZoneId Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  
ZoneOffset Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  between Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  %createRequestPermissionResultContract Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  	emptyList Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  getHealthConnectClient Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  getOrCreate Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  getReadPermission Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  getSdkStatus Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  getStepsPermissions Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  getWritePermission Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  listOf Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  map Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  setOf Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  withContext Fcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.Companion  	AVAILABLE Vcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.HealthConnectAvailability  NEEDS_UPDATE Vcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.HealthConnectAvailability  
NOT_SUPPORTED Vcom.watchrx.watchrxhealth.healthconnect.HealthConnectManager.HealthConnectAvailability  Boolean  com.watchrx.watchrxhealth.twilio  JvmName  com.watchrx.watchrxhealth.twilio  any  com.watchrx.watchrxhealth.twilio  equals  com.watchrx.watchrxhealth.twilio  isH264Supported  com.watchrx.watchrxhealth.twilio  LogUtils com.watchrx.watchrxhealth.utils  debug (com.watchrx.watchrxhealth.utils.LogUtils  ByteArrayOutputStream java.io  IOException java.io  toByteArray java.io.ByteArrayOutputStream  printStackTrace java.io.IOException  	Exception 	java.lang  message java.lang.Exception  
ByteBuffer java.nio  capacity java.nio.Buffer  limit java.nio.Buffer  position java.nio.Buffer  capacity java.nio.ByteBuffer  get java.nio.ByteBuffer  position java.nio.ByteBuffer  put java.nio.ByteBuffer  wrap java.nio.ByteBuffer  Instant 	java.time  	LocalDate 	java.time  ZoneId 	java.time  
ZoneOffset 	java.time  atStartOfDay java.time.LocalDate  now java.time.LocalDate  plusDays java.time.LocalDate  rules java.time.ZoneId  
systemDefault java.time.ZoneId  
systemDefault java.time.ZoneOffset  	toInstant java.time.ZonedDateTime  	getOffset java.time.zone.ZoneRules  Array kotlin  	ByteArray kotlin  	Function1 kotlin  IntArray kotlin  Nothing kotlin  Result kotlin  String kotlin  arrayOf kotlin  
intArrayOf kotlin  map kotlin  get kotlin.Array  get kotlin.ByteArray  set kotlin.ByteArray  size kotlin.ByteArray  rem 
kotlin.Double  toInt 
kotlin.Double  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  get kotlin.IntArray  	compareTo kotlin.Long  div kotlin.Long  rem kotlin.Long  toString kotlin.Long  	Companion 
kotlin.String  equals 
kotlin.String  format 
kotlin.String  replace 
kotlin.String  toRegex 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  IntIterator kotlin.collections  List kotlin.collections  Set kotlin.collections  any kotlin.collections  	emptyList kotlin.collections  listOf kotlin.collections  map kotlin.collections  setOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  map kotlin.collections.List  size kotlin.collections.List  containsAll kotlin.collections.Set  SuspendFunction1 kotlin.coroutines  JvmName 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  Sequence kotlin.sequences  any kotlin.sequences  map kotlin.sequences  Regex kotlin.text  any kotlin.text  equals kotlin.text  format kotlin.text  map kotlin.text  replace kotlin.text  toRegex kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  AggregateRequest !kotlinx.coroutines.CoroutineScope  	LocalDate !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  LogUtils !kotlinx.coroutines.CoroutineScope  ReadRecordsRequest !kotlinx.coroutines.CoroutineScope  StepData !kotlinx.coroutines.CoroutineScope  StepsRecord !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  TimeRangeFilter !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  View !kotlinx.coroutines.CoroutineScope  ZoneId !kotlinx.coroutines.CoroutineScope  
ZoneOffset !kotlinx.coroutines.CoroutineScope  between !kotlinx.coroutines.CoroutineScope  btnGrantPermissions !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  formatStepsCount !kotlinx.coroutines.CoroutineScope  getHealthConnectClient !kotlinx.coroutines.CoroutineScope  getStepsPermissions !kotlinx.coroutines.CoroutineScope  healthConnectManager !kotlinx.coroutines.CoroutineScope  listOf !kotlinx.coroutines.CoroutineScope  
loadStepsData !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  setOf !kotlinx.coroutines.CoroutineScope  showErrorMessage !kotlinx.coroutines.CoroutineScope  "showSampleDataWithPermissionPrompt !kotlinx.coroutines.CoroutineScope  tvCaloriesTime !kotlinx.coroutines.CoroutineScope  tvCaloriesValue !kotlinx.coroutines.CoroutineScope  tvHeartRateTime !kotlinx.coroutines.CoroutineScope  tvHeartRateValue !kotlinx.coroutines.CoroutineScope  tvNoData !kotlinx.coroutines.CoroutineScope  tvSleepTime !kotlinx.coroutines.CoroutineScope  tvSleepValue !kotlinx.coroutines.CoroutineScope  tvStepsCount !kotlinx.coroutines.CoroutineScope  tvStepsValue !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  HardwareVideoDecoderFactory 
tvi.webrtc  HardwareVideoEncoderFactory 
tvi.webrtc  
VideoFrame 
tvi.webrtc  YuvConverter 
tvi.webrtc  supportedCodecs &tvi.webrtc.HardwareVideoDecoderFactory  supportedCodecs &tvi.webrtc.HardwareVideoEncoderFactory  supportedCodecs (tvi.webrtc.MediaCodecVideoDecoderFactory  name tvi.webrtc.VideoCodecInfo  Bitmap tvi.webrtc.VideoFrame  
BitmapFactory tvi.webrtc.VideoFrame  Build tvi.webrtc.VideoFrame  ByteArrayOutputStream tvi.webrtc.VideoFrame  
ByteBuffer tvi.webrtc.VideoFrame  
I420Buffer tvi.webrtc.VideoFrame  ImageDecoder tvi.webrtc.VideoFrame  Matrix tvi.webrtc.VideoFrame  Rect tvi.webrtc.VideoFrame  
TextureBuffer tvi.webrtc.VideoFrame  YuvConverter tvi.webrtc.VideoFrame  buffer tvi.webrtc.VideoFrame  i420ToYuvImage tvi.webrtc.VideoFrame  rotation tvi.webrtc.VideoFrame  height tvi.webrtc.VideoFrame.Buffer  toI420 tvi.webrtc.VideoFrame.Buffer  width tvi.webrtc.VideoFrame.Buffer  dataU  tvi.webrtc.VideoFrame.I420Buffer  dataV  tvi.webrtc.VideoFrame.I420Buffer  dataY  tvi.webrtc.VideoFrame.I420Buffer  strideU  tvi.webrtc.VideoFrame.I420Buffer  strideV  tvi.webrtc.VideoFrame.I420Buffer  strideY  tvi.webrtc.VideoFrame.I420Buffer  convert tvi.webrtc.YuvConverter  release tvi.webrtc.YuvConverter  permissionLauncher android.app.Activity  showPermissionPrompt android.app.Activity  permissionLauncher android.content.Context  showPermissionPrompt android.content.Context  permissionLauncher android.content.ContextWrapper  showPermissionPrompt android.content.ContextWrapper  permissionLauncher  android.view.ContextThemeWrapper  showPermissionPrompt  android.view.ContextThemeWrapper  permissionLauncher #androidx.activity.ComponentActivity  showPermissionPrompt #androidx.activity.ComponentActivity  permissionLauncher (androidx.appcompat.app.AppCompatActivity  showPermissionPrompt (androidx.appcompat.app.AppCompatActivity  permissionLauncher #androidx.core.app.ComponentActivity  showPermissionPrompt #androidx.core.app.ComponentActivity  permissionLauncher &androidx.fragment.app.FragmentActivity  showPermissionPrompt &androidx.fragment.app.FragmentActivity  permissionLauncher com.watchrx.watchrxhealth  showPermissionPrompt com.watchrx.watchrxhealth  showPermissionPrompt .com.watchrx.watchrxhealth.HealthVitalsActivity  permissionLauncher 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  showPermissionPrompt 8com.watchrx.watchrxhealth.HealthVitalsActivity.Companion  permissionLauncher !kotlinx.coroutines.CoroutineScope  showPermissionPrompt !kotlinx.coroutines.CoroutineScope  Button android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  TextView android.app.Activity  finish android.app.Activity  	setResult android.app.Activity  Button android.content.Context  RESULT_CANCELED android.content.Context  	RESULT_OK android.content.Context  TextView android.content.Context  Button android.content.ContextWrapper  RESULT_CANCELED android.content.ContextWrapper  	RESULT_OK android.content.ContextWrapper  TextView android.content.ContextWrapper  Button  android.view.ContextThemeWrapper  RESULT_CANCELED  android.view.ContextThemeWrapper  	RESULT_OK  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  Button #androidx.activity.ComponentActivity  RESULT_CANCELED #androidx.activity.ComponentActivity  	RESULT_OK #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Button (androidx.appcompat.app.AppCompatActivity  RESULT_CANCELED (androidx.appcompat.app.AppCompatActivity  	RESULT_OK (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  Button #androidx.core.app.ComponentActivity  RESULT_CANCELED #androidx.core.app.ComponentActivity  	RESULT_OK #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Button &androidx.fragment.app.FragmentActivity  RESULT_CANCELED &androidx.fragment.app.FragmentActivity  	RESULT_OK &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  PermissionsRationaleActivity com.watchrx.watchrxhealth  RESULT_CANCELED com.watchrx.watchrxhealth  	RESULT_OK com.watchrx.watchrxhealth  Bundle 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  Button 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  LogUtils 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  R 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  RESULT_CANCELED 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  	RESULT_OK 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  TextView 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  findViewById 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  finish 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  setContentView 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  	setResult 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  
setupViews 6com.watchrx.watchrxhealth.PermissionsRationaleActivity  LogUtils @com.watchrx.watchrxhealth.PermissionsRationaleActivity.Companion  R @com.watchrx.watchrxhealth.PermissionsRationaleActivity.Companion  RESULT_CANCELED @com.watchrx.watchrxhealth.PermissionsRationaleActivity.Companion  	RESULT_OK @com.watchrx.watchrxhealth.PermissionsRationaleActivity.Companion  
btn_cancel com.watchrx.watchrxhealth.R.id  btn_continue com.watchrx.watchrxhealth.R.id  tv_rationale_description com.watchrx.watchrxhealth.R.id  tv_rationale_title com.watchrx.watchrxhealth.R.id  activity_permissions_rationale "com.watchrx.watchrxhealth.R.layout                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     