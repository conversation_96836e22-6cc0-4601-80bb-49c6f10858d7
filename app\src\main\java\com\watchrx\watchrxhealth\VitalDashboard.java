package com.watchrx.watchrxhealth;

import android.Manifest;
import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.VitalListAdapter;
import com.watchrx.watchrxhealth.ble.NewVitalsActivity;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.LatestVitalDataModel;
import com.watchrx.watchrxhealth.models.VitalDataModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.OnItemClickListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.watchrx.watchrxhealth.healthconnect.HealthConnectManager;

public class VitalDashboard extends AppCompatActivity {
    private RecyclerView recyclerView;
    private ProgressBar progressBar;
    private VitalListAdapter adapter;
    private TextView noItemsMessage;

    public int needRequestPermission;
    private static final int PERMISSION_REQUEST_COARSE_LOCATION = 1;

    private BluetoothAdapter bluetoothAdapter;
    private HealthConnectManager healthConnectManager;
    private ActivityResultLauncher<Set<String>> healthConnectPermissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        setContentView(R.layout.activity_vital_dashboard);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("My Vitals");

        recyclerView = findViewById(R.id.vital_list);
        progressBar = findViewById(R.id.loader);
        noItemsMessage = findViewById(R.id.no_items_message);

        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(layoutManager);
        showLoading(true);
        getLatestVitalInfo();
        setBottomNavigation();
        setUpBluetoothConfig();
        
        // Initialize Health Connect Manager
        healthConnectManager = new HealthConnectManager(this);
        setupHealthConnectPermissionLauncher();

        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        if (bluetoothAdapter == null) {
            return;
        }

        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                return;
            }
            startActivity(enableBtIntent);
        }
    }

    private final ActivityResultLauncher<Intent> enableBluetoothLauncher =
            registerForActivityResult(new ActivityResultContracts.StartActivityForResult(),
                    result -> {
                        if (result.getResultCode() == Activity.RESULT_OK) {
                            // Bluetooth enabled successfully
                        } else {
                            // Bluetooth enabling failed or canceled
                        }
                    });

    public void enableBluetoothApi30(Activity activity) {
        if (bluetoothAdapter == null) {
            return; // Device does not support Bluetooth
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.BLUETOOTH_CONNECT)
                    != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(activity,
                        new String[]{Manifest.permission.BLUETOOTH_CONNECT}, 100);
                return;
            }
        }

        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            enableBluetoothLauncher.launch(enableBtIntent);
        }
    }

    private void setUpBluetoothConfig() {
        int requestedCnt = 0;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestedCnt = 3;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            requestedCnt = 2;
        }

        if (requestedCnt > 0) {
            if (needRequestPermission == 0) {
                needRequestPermission = !checkPermissions() ? 1 : requestedCnt;
                if (needRequestPermission == 1) {
                    requestPermissions();
                }
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.M)
    public boolean checkPermissions() {
        boolean result = true;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (checkSelfPermission(android.Manifest.permission.BLUETOOTH_SCAN)
                    != PackageManager.PERMISSION_GRANTED ||
                    checkSelfPermission(android.Manifest.permission.BLUETOOTH_CONNECT)
                            != PackageManager.PERMISSION_GRANTED) {
                result = false;
            }
        } else {
            if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED) {
                result = false;
            }
        }

        if (result) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                if (checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        != PackageManager.PERMISSION_GRANTED ||
                        checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE)
                                != PackageManager.PERMISSION_GRANTED) {
                    result = false;
                }
            }
        }
        return result;
    }

    @TargetApi(Build.VERSION_CODES.M)
    public void requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(new String[]{
                            Manifest.permission.BLUETOOTH_SCAN,
                            Manifest.permission.BLUETOOTH_CONNECT},
                    PERMISSION_REQUEST_COARSE_LOCATION);
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                requestPermissions(new String[]{
                                Manifest.permission.ACCESS_FINE_LOCATION},
                        PERMISSION_REQUEST_COARSE_LOCATION);
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    requestPermissions(new String[]{
                                    Manifest.permission.ACCESS_FINE_LOCATION,
                                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                    Manifest.permission.READ_EXTERNAL_STORAGE},
                            PERMISSION_REQUEST_COARSE_LOCATION);
                }
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (grantResults.length == 0) {
            return;
        }
        if (requestCode == PERMISSION_REQUEST_COARSE_LOCATION) {
            for (int i = 0; i < permissions.length; i++) {
                if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                    if (permissions[i].equals(Manifest.permission.BLUETOOTH_SCAN) ||
                            permissions[i].equals(Manifest.permission.BLUETOOTH_CONNECT)) {
                        if (needRequestPermission >= 1) {
                            needRequestPermission++;
                        }
                    } else if (permissions[i].equals(Manifest.permission.ACCESS_FINE_LOCATION)) {
                        if (needRequestPermission == 1) {
                            needRequestPermission = 2;
                        }
                    }
                }
            }
        }
    }

    private void showLoading(boolean isLoading) {
        if (isLoading) {
            progressBar.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
            noItemsMessage.setVisibility(View.GONE);
        } else {
            progressBar.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
        }
    }

    private void showNoItemsMessage(boolean show) {
        if (show) {
            noItemsMessage.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
        } else {
            noItemsMessage.setVisibility(View.GONE);
        }
    }


    private void getLatestVitalInfo() {
        try {
            LogUtils.debug("Going to getLatestVitalInfo");
            URL url = new URL(URLConstants.LATEST_VITAL_DATA);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", patientId);
            new RestAsyncTask(url, jsonObject.toString(), null, new LatestVitalInfoResponseHandler(), null).execute();
        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
            e.printStackTrace();
        }
    }


    private class LatestVitalInfoResponseHandler implements TaskResultHandler {
        @SuppressLint("NotifyDataSetChanged")
        @Override
        public void handleResult(HandlerResult handlerResult) {
            showLoading(false);
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }

            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        LatestVitalDataModel responseMessage = new Gson().fromJson((String) result, LatestVitalDataModel.class);
                        if (responseMessage != null && responseMessage.isStatus()
                                && responseMessage.getData() != null && responseMessage.getData().size() > 0) {
                            List<VitalDataModel> vitalList = new ArrayList<>(responseMessage.data);
                            Map<String, Integer> vitalTypeToImageMap = new HashMap<>();
                            vitalTypeToImageMap.put("Heart Rate", R.drawable.ic_heart_rate);
                            vitalTypeToImageMap.put("Pedometer", R.drawable.ic_baseline_directions_walk_24);
                            vitalTypeToImageMap.put("Weight", R.drawable.ic_weight_40);
                            vitalTypeToImageMap.put("Temperature", R.drawable.ic_thermo_40);
                            vitalTypeToImageMap.put("Random Blood Sugar", R.drawable.ic_blood_sugar);
                            vitalTypeToImageMap.put("Fasting Blood Sugar", R.drawable.ic_blood_sugar);
                            vitalTypeToImageMap.put("Oxygen Saturation", R.drawable.ic_baseline_bloodtype_24);
                            vitalTypeToImageMap.put("Blood Pressure", R.drawable.bp);
                            vitalTypeToImageMap.put("Sleep Monitor", R.drawable.sleep);

                            for (VitalDataModel vitalDataModel : vitalList) {
                                String vitalType = vitalDataModel.getVitalType();
                                if (vitalTypeToImageMap.containsKey(vitalType)) {
                                    Integer imageRes = vitalTypeToImageMap.get(vitalType);
                                    if (imageRes != null) {
                                        vitalDataModel.setImage(imageRes);
                                    }
                                }
                            }
                            recyclerView.setAdapter(new VitalListAdapter(vitalList, new OnItemClickListener() {
                                @Override
                                public void onItemClicked(VitalDataModel item) {
                                    // Handle Health Connect steps click
                                    if (item.getVitalType().contains("Health Connect")) {
                                        requestHealthConnectPermissions();
                                        return;
                                    }
                                    
                                    Intent intent = new Intent(VitalDashboard.this, VitalDetailsActivity.class);
                                    if (item.getVitalType().equalsIgnoreCase("Random Blood Sugar") ||
                                            item.getVitalType().equalsIgnoreCase("Fasting Blood Sugar")) {
                                        intent.putExtra("vitalType", "Blood Sugar");
                                    } else {
                                        intent.putExtra("vitalType", item.getVitalType());
                                    }
                                    startActivity(intent);
                                }

                                @Override
                                public void onButtonClicked(VitalDataModel item) {
                                    // Handle Health Connect steps button click
                                    if (item.getVitalType().contains("Health Connect")) {
                                        requestHealthConnectPermissions();
                                        return;
                                    }
                                    
                                    String vitalType = "";
                                    if (item.getVitalType().equalsIgnoreCase("Random Blood Sugar") ||
                                            item.getVitalType().equalsIgnoreCase("Fasting Blood Sugar")) {
                                        vitalType = "Blood Sugar";
                                    } else {
                                        vitalType = item.getVitalType();
                                    }
                                    String deviceName = CommUtils.getMedicalDeviceType(vitalType);
                                    if (deviceName == null) {
                                        Toast.makeText(VitalDashboard.this, "Medical device type not found", Toast.LENGTH_SHORT).show();
                                        return;
                                    }
                                    Intent intent = new Intent(VitalDashboard.this, NewVitalsActivity.class);
                                    intent.putExtra("vitalScheduleId", "");
                                    intent.putExtra("deviceName", deviceName);
                                    intent.putExtra("vitalTypeName", vitalType);
                                    startActivity(intent);
                                }
                            }));
                            
                            // Add Health Connect steps data
                            addHealthConnectStepsData(vitalList);
                            
                            recyclerView.invalidate();
                            Objects.requireNonNull(recyclerView.getAdapter()).notifyDataSetChanged();
                        } else {
                            showNoItemsMessage(true);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        getLatestVitalInfo();
    }

    private void setBottomNavigation() {
        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }
    
    private void addHealthConnectStepsData(List<VitalDataModel> vitalList) {
        if (healthConnectManager == null) {
            return;
        }
        
        // Check if Health Connect is available
        if (healthConnectManager.checkHealthConnectAvailability() != HealthConnectManager.HealthConnectAvailability.AVAILABLE) {
            LogUtils.debug("Health Connect is not available");
            return;
        }
        
        // Check permissions and load actual steps data in background
        new Thread(() -> {
            try {
                VitalDataModel stepsVital = new VitalDataModel();
                stepsVital.setVitalType("Steps (Health Connect)");
                stepsVital.setImage(R.drawable.ic_baseline_directions_walk_24);
                
                // Check if we have Health Connect permissions using reflection to handle Kotlin suspend function
                java.lang.reflect.Method hasPermissionsMethod = healthConnectManager.getClass().getMethod("hasStepsPermissions", kotlin.coroutines.Continuation.class);
                java.lang.reflect.Method getTodayStepsMethod = healthConnectManager.getClass().getMethod("getTodaySteps", kotlin.coroutines.Continuation.class);
                
                // For now, add a basic entry - will be updated with actual data when permissions are granted
                boolean hasPermissions = false;
                long todaySteps = 0;
                
                try {
                    // Simple check - if we can create the client without error, assume we have basic access
                    // The actual permission check will happen when user clicks
                    hasPermissions = true; // We'll show connect button if needed
                    todaySteps = 0; // Will be loaded after permissions granted
                } catch (Exception e) {
                    LogUtils.debug("No Health Connect permissions: " + e.getMessage());
                }
                
                if (hasPermissions && todaySteps > 0) {
                    stepsVital.setVitalData(String.valueOf(todaySteps));
                } else {
                    stepsVital.setVitalData("Connect to view");
                }
                
                stepsVital.setDate(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                
                runOnUiThread(() -> {
                    // Add to the beginning of the list
                    vitalList.add(0, stepsVital);
                    
                    // Update the adapter
                    if (recyclerView.getAdapter() != null) {
                        recyclerView.getAdapter().notifyDataSetChanged();
                    }
                    
                    LogUtils.debug("Added Health Connect steps to vitals list");
                });
                
            } catch (Exception e) {
                LogUtils.debug("Error adding Health Connect steps: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }
    
    private void setupHealthConnectPermissionLauncher() {
        healthConnectPermissionLauncher = registerForActivityResult(
            healthConnectManager.createPermissionRequestContract(),
            grantedPermissions -> {
                LogUtils.debug("Health Connect permission result: " + grantedPermissions);
                if (grantedPermissions.containsAll(healthConnectManager.getStepsPermissions())) {
                    Toast.makeText(this, "Health Connect permissions granted! Refreshing vitals...", Toast.LENGTH_SHORT).show();
                    // Refresh the vitals list to load actual steps data
                    getLatestVitalInfo();
                } else {
                    Toast.makeText(this, "Health Connect permissions are required to show steps data", Toast.LENGTH_LONG).show();
                }
            }
        );
    }
    
    private void requestHealthConnectPermissions() {
        try {
            Set<String> permissions = healthConnectManager.getStepsPermissions();
            LogUtils.debug("Requesting Health Connect permissions: " + permissions);
            healthConnectPermissionLauncher.launch(permissions);
        } catch (Exception e) {
            LogUtils.debug("Error requesting Health Connect permissions: " + e.getMessage());
            Toast.makeText(this, "Error requesting Health Connect permissions", Toast.LENGTH_SHORT).show();
        }
    }
}