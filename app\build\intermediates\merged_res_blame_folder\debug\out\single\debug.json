[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_thermo_40.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_thermo_40.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_watch.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\watch.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_pgoress_screen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\pgoress_screen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_pedometer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_pedometer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_custom_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\custom_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_comments.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\comments.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_phn.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\phn.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_cancel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_cancel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_dash_doc.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\dash_doc.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_rounded_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\rounded_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_red_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_red_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_call_end.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\call_end.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_plus.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_plus.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-hdpi_watchrx_app_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-hdpi\\watchrx_app_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_vital_list_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\vital_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_low_bat_green.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\low_bat_green.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_reminder_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\reminder_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_med_details_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\med_details_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_vitals_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_vitals_icon.xml"}, {"merged": "com.watchrx.watchrxhealth.app-merged_res-60:/layout_activity_main_dashboard.xml.flat", "source": "com.watchrx.watchrxhealth.app-main-62:/layout/activity_main_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_success.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_success.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_content_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\content_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_unmute_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\unmute_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_rounded_corners.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\rounded_corners.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_dummy_lay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\dummy_lay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_item_event.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\item_event.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_user_icon_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\user_icon_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\xml_accessibility_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\xml\\accessibility_service_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_spo2_40.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_spo2_40.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxhdpi_watchrx_app_icon_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxhdpi\\watchrx_app_icon_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_medical.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_medical.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_dash_chat.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\dash_chat.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_videocam_off_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_videocam_off_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_heart_rate.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_heart_rate.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_sleep.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\sleep.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_light.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_light.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_background_with_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\background_with_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_icon_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_icon_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_bg_topheader.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_bg_topheader.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_turn_off.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\turn_off.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_layout_corner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\layout_corner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_yes.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\yes.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_selector_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_selector_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_contact_list_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\contact_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bluetooth_connected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bluetooth_connected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_medicine.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\medicine.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_get_latest_measurement_record.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\get_latest_measurement_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxhdpi_watchrx_app_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxhdpi\\watchrx_app_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_permissions_rationale.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_permissions_rationale.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxxhdpi_watchrx_app_icon_background.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxxhdpi\\watchrx_app_icon_background.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_heart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_heart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_g_p_s.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_g_p_s.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_call_end_white_24px.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_call_end_white_24px.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_text_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_text_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_button_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\button_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_weigt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_weigt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_call_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_call_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-hdpi_ic_loading.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-hdpi\\ic_loading.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_selected_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_selected_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_txt_username.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_txt_username.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_calorie.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_calorie.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_volume_up_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_volume_up_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_meds.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_meds.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_contact.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_contact.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_bluetooth_device_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\bluetooth_device_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_phone.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\phone.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_dialog_loading.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\dialog_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_sleep_monitor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_sleep_monitor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_alert_list_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\alert_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_device_thermostat_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_device_thermostat_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_organge_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_organge_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-hdpi_watchrx_app_icon_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-hdpi\\watchrx_app_icon_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_weight_40.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_weight_40.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_battery.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_battery.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_italic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_italic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_medical_assistance.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\medical_assistance.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_wifi_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_wifi_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_fm_person.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\fm_person.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_drugs.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\drugs.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\menu_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\menu\\refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_warning.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\warning.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_vitals_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_vitals_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_vital_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_vital_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_reminder_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_reminder_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_shape_bg_incoming_bubble.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\shape_bg_incoming_bubble.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_mic_off_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_mic_off_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_custom_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\custom_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_selected_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_selected_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_rounded_logo_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\rounded_logo_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_chat_40.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_chat_40.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_circle_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_circle_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_add_medication.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_add_medication.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_round_bloodpressure_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_round_bloodpressure_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxhdpi_watchrx_app_icon_background.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxhdpi\\watchrx_app_icon_background.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_bluetooth_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_bluetooth_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_full_bat_green.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\full_bat_green.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_down_arrow.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\down_arrow.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_vital_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_vital_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\xml_custom_prog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\xml\\custom_prog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_thermo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_thermo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_received_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_received_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_reminder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_reminder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_temp_f.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_temp_f.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_pedometer_new.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_pedometer_new.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_light_italic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_light_italic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_contacts_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_contacts_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_layout_confirmation_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\layout_confirmation_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_spo2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_spo2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_deafult_medicine.PNG.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\deafult_medicine.PNG"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_black_italic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_black_italic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\raw_amplifyconfiguration.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\raw\\amplifyconfiguration.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\xml_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\xml\\settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_vital_details_lit_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\vital_details_lit_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_answer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_answer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-hdpi_watchrx_app_icon_background.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-hdpi\\watchrx_app_icon_background.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_video_call_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_video_call_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_eye.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_eye.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\xml_filepaths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\xml\\filepaths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_interaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_interaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_sample_three_icons.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\sample_three_icons.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_medication.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_medication.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_nurse.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\nurse.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_video_call.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_video_call.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_mic_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_mic_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_help_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_help_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_text_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_text_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_green_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_green_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_no_alerts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_no_alerts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_doctor.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\doctor.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxxhdpi_watchrx_app_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxxhdpi\\watchrx_app_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-mdpi_watchrx_app_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-mdpi\\watchrx_app_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_phonelink_ring_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_phonelink_ring_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_videocam_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_videocam_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_blue_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\blue_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_vital_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\vital_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_switch_camera_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_switch_camera_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_selector_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_selector_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_custom_day_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\custom_day_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_medication.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_medication.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxhdpi_watchrx_app_icon_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxhdpi\\watchrx_app_icon_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_resend_otpscreen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_resend_otpscreen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_person_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_person_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_incoming_call.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_incoming_call.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxhdpi_ic_blue_connected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxhdpi\\ic_blue_connected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_text_phone.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\text_phone.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_enter_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_enter_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_message.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\message.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_sms_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_sms_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_rounded_corner_img.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\rounded_corner_img.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_mail.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\mail.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_thin.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_thin.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\menu_menu_patient_diary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\menu\\menu_patient_diary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bp.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bp.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_answer_list_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\answer_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_zoom_video_call.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_zoom_video_call.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xhdpi_watchrx_app_icon_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xhdpi\\watchrx_app_icon_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_custom_progress_dialog_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\custom_progress_dialog_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_navigate_next.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_navigate_next.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_sleep_monitor.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\sleep_monitor.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_vital_signs.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\vital_signs.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_cutsom_progress_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\cutsom_progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_bluetooth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_bluetooth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_enter_otpatictivity.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_enter_otpatictivity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_chat_40_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_chat_40_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\color_toggle_text_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\color\\toggle_text_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_speaker_on.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\speaker_on.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_photo_user.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_photo_user.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bluetooth_orange.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bluetooth_orange.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_summary_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_summary_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_stop_call.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_stop_call.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_vitals_measurement.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_vitals_measurement.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_edittext.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_edittext.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_icons8_thermometer_40.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\icons8_thermometer_40.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_mic_off.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\mic_off.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_custom_reply_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\custom_reply_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_heart_rate_pink.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_heart_rate_pink.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_dial_pink1_blue.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\dial_pink1_blue.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_button_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\button_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_my_task_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_my_task_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_dial_pink.PNG.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\dial_pink.PNG"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\anim_rotate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\anim\\rotate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_heart_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_heart_pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-mdpi_watchrx_app_icon_background.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-mdpi\\watchrx_app_icon_background.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_vital_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_vital_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_meds_filled.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_meds_filled.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_question.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_question.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_mic_on.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\mic_on.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_dial.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\dial.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_center_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\center_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_voice_assistance.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\voice_assistance.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_video_call_actiity.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_video_call_actiity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_latest_alerts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_latest_alerts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bordered_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bordered_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_notifications.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_emergency.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_emergency.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-hdpi_watchrx_app_icon_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-hdpi\\watchrx_app_icon_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_user.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\user.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_reminder.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\reminder.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xhdpi_watchrx_app_icon_background.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xhdpi\\watchrx_app_icon_background.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_clock.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\clock.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_patient_diary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_patient_diary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_button_shape_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\button_shape_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_button_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\button_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_health_vitals.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_health_vitals.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_error_outline_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_error_outline_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_mic_white_off_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_mic_white_off_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xhdpi_watchrx_app_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xhdpi\\watchrx_app_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_new_vitals.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_new_vitals.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_text.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\text.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_medical_services_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_medical_services_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_item_date.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\item_date.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_paracetmaol.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\paracetmaol.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_custom_action_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\custom_action_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_foot_steps.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_foot_steps.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_geo_fecne.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\geo_fecne.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-anydpi-v26_watchrx_app_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-anydpi-v26\\watchrx_app_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_gray_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\gray_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_marker_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\marker_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_call_end_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_call_end_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_bluetooth_devices.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_bluetooth_devices.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_headset_mic_white_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_headset_mic_white_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_thermo_64.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_thermo_64.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_bold_italic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_bold_italic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_unselected_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_unselected_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_vitals_health_connect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_vitals_health_connect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_circular_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\circular_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_answer_item_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\answer_item_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\menu_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\menu\\menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_right_arrow.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\right_arrow.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_chat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_chat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_speaker_off.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\speaker_off.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_main_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_main_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_chat_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_chat_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_medications_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_medications_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_txt_password.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_txt_password.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_bloodtype_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_bloodtype_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxxhdpi_watchrx_app_icon_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxxhdpi\\watchrx_app_icon_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_notification_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\notification_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_nurse_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\nurse_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_custom_alerts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_custom_alerts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_mute_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\mute_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_shape_bg_outgoing_bubble.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\shape_bg_outgoing_bubble.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_blood_sugar.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_blood_sugar.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_appointment_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_appointment_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_call.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_call.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_button_shape_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\button_shape_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_favorite_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_favorite_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_progress_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\progress_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_nurse_2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\nurse_2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bottom_navigation_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bottom_navigation_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_medication_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_medication_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_appointment_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_appointment_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_heart_rate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_heart_rate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_pedometer_item_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\pedometer_item_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_send.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\send.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xxxhdpi_watchrx_app_icon_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xxxhdpi\\watchrx_app_icon_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_vital.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_vital.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_mute_sound.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\mute_sound.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_sound.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_sound.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_capsules.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\capsules.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_web_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_web_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_medication_list_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\medication_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_later.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\later.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_text_center.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\text_center.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_text_answer_sq.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\text_answer_sq.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_purple_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_purple_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_fill_heart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_fill_heart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_app_logo_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\app_logo_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_conta_name_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\conta_name_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_support_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\support_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_rounded_edittext.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\rounded_edittext.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_item_count.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\item_count.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_medicine.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_medicine.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_nurse_on_the_way.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_nurse_on_the_way.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_reminders_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_reminders_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_item_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\item_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_green_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\green_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_sent_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_sent_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\menu_menu_video_activity.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\menu\\menu_video_activity.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_thin_italic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_thin_italic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_custom_calendar_day.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\custom_calendar_day.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_unselected_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_unselected_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_preference_dialog_number_edittext.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\preference_dialog_number_edittext.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_resend_otp_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\resend_otp_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_sample_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\sample_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_view_text_message_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\view_text_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-anydpi-v26_watchrx_app_icon_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-anydpi-v26\\watchrx_app_icon_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_imeiwarning.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\imeiwarning.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_alarm_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_alarm_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_call_open.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\call_open.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_font.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\font.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_call_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\call_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_support.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\support.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_login_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\login_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_alert.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_alert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_visit_verification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_visit_verification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_item_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\item_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_messages_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_messages_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\raw_video_call_tone.mpeg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\raw\\video_call_tone.mpeg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_dialog_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\dialog_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_send_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\send_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_blue_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\blue_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-mdpi_watchrx_app_icon_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-mdpi\\watchrx_app_icon_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_black.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_black.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_appointment.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\appointment.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_daughter_reminder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_daughter_reminder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_home_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\home_logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_no.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\no.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_eye_off.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_eye_off.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_custome_loader.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\custome_loader.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_chat_1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_chat_1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bluetooth_searching.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bluetooth_searching.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_calendar.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\calendar.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_dot.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_dot.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_heart_filled.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_heart_filled.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_icon_semitrans_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_icon_semitrans_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_directions_walk_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_directions_walk_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_ic_baseline_refresh_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\ic_baseline_refresh_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\raw_awsconfiguration.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\raw\\awsconfiguration.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_login_screen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_login_screen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\font_lato_bold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\font\\lato_bold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_zoom_video_call_screen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_zoom_video_call_screen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_interactive_voice.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_interactive_voice.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-xhdpi_watchrx_app_icon_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-xhdpi\\watchrx_app_icon_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_reply_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\reply_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\layout_activity_view_all_text_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\layout\\activity_view_all_text_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\mipmap-mdpi_watchrx_app_icon_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\mipmap-mdpi\\watchrx_app_icon_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_item_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\item_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_toggle_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\toggle_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_btn_welcome.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_btn_welcome.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_bg_btn_login.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\bg_btn_login.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_utensils.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\utensils.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_list_view_custom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\list_view_custom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-merged_res-60:\\drawable_icons_meds_40.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-main-62:\\drawable\\icons_meds_40.png"}]