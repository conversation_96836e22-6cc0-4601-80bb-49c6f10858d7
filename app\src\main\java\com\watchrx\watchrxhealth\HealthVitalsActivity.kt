package com.watchrx.watchrxhealth

import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.watchrx.watchrxhealth.healthconnect.HealthConnectManager
import com.watchrx.watchrxhealth.utils.LogUtils
import kotlinx.coroutines.launch

class HealthVitalsActivity : AppCompatActivity() {
    companion object {

        private const val TAG = "HealthVitalsActivity"
    }
    
    private lateinit var healthConnectManager: HealthConnectManager
    
    // UI components
    private lateinit var tvStepsValue: TextView
    private lateinit var tvStepsCount: TextView
    private lateinit var tvHeartRateValue: TextView
    private lateinit var tvHeartRateTime: TextView
    private lateinit var tvCaloriesValue: TextView
    private lateinit var tvCaloriesTime: TextView
    private lateinit var tvSleepValue: TextView
    private lateinit var tvSleepTime: TextView
    private lateinit var tvNoData: TextView
    private lateinit var btnGrantPermissions: Button
    
    // Permission launcher
    private lateinit var permissionLauncher: ActivityResultLauncher<Set<String>>
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_health_vitals)
        
        initializeHealthConnect()
        initializeViews()
        setupPermissionLauncher()
        checkHealthConnectAndLoadData()
    }
    
    private fun initializeHealthConnect() {
        healthConnectManager = HealthConnectManager(this)
    }
    
    private fun initializeViews() {
        tvStepsValue = findViewById(R.id.tv_steps_value)
        tvStepsCount = findViewById(R.id.tv_steps_count)
        tvHeartRateValue = findViewById(R.id.tv_heart_rate_value)
        tvHeartRateTime = findViewById(R.id.tv_heart_rate_time)
        tvCaloriesValue = findViewById(R.id.tv_calories_value)
        tvCaloriesTime = findViewById(R.id.tv_calories_time)
        tvSleepValue = findViewById(R.id.tv_sleep_value)
        tvSleepTime = findViewById(R.id.tv_sleep_time)
        tvNoData = findViewById(R.id.tv_no_data)
        btnGrantPermissions = findViewById(R.id.btn_grant_permissions)
        
        btnGrantPermissions.setOnClickListener {
            requestHealthConnectPermissions()
        }
    }
    
    private fun setupPermissionLauncher() {
        permissionLauncher = registerForActivityResult(
            healthConnectManager.createPermissionRequestContract()
        ) { grantedPermissions: Set<String> ->
            LogUtils.debug("Permission result: $grantedPermissions")
            if (grantedPermissions.containsAll(healthConnectManager.getStepsPermissions())) {
                // Permissions granted
                btnGrantPermissions.visibility = View.GONE
                loadStepsData()
                Toast.makeText(this, "Health Connect permissions granted!", Toast.LENGTH_SHORT).show()
            } else {
                // Permissions not granted
                Toast.makeText(this, "Health Connect permissions are required to show steps data", Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun checkHealthConnectAndLoadData() {
        when (healthConnectManager.checkHealthConnectAvailability()) {
            HealthConnectManager.HealthConnectAvailability.NOT_SUPPORTED -> {
                showNotSupportedMessage()
                return
            }
            HealthConnectManager.HealthConnectAvailability.NEEDS_UPDATE -> {
                showNeedsUpdateMessage()
                return
            }
            HealthConnectManager.HealthConnectAvailability.AVAILABLE -> {
                // Check permissions and load data
                lifecycleScope.launch {
                    if (healthConnectManager.hasStepsPermissions()) {
                        btnGrantPermissions.visibility = View.GONE
                        loadStepsData()
                    } else {
                        showPermissionPrompt()
                        LogUtils.debug("Health Connect available but permissions not granted")
                    }
                }
            }
        }
    }
    
    private fun requestHealthConnectPermissions() {
        lifecycleScope.launch {
            try {
                val permissions = healthConnectManager.getStepsPermissions()
                LogUtils.debug("Requesting permissions: $permissions")
                permissionLauncher.launch(permissions)
            } catch (e: Exception) {
                LogUtils.debug("Error requesting permissions: ${e.message}")
                Toast.makeText(this@HealthVitalsActivity, "Error requesting Health Connect permissions", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun loadStepsData() {
        lifecycleScope.launch {
            try {
                LogUtils.debug("Loading steps data from Health Connect")
                
                // Hide no data message and permission button
                tvNoData.visibility = View.GONE
                btnGrantPermissions.visibility = View.GONE
                
                // Load today's steps
                val todaySteps = healthConnectManager.getTodaySteps()
                
                // Update UI with real data
                tvStepsValue.text = formatStepsCount(todaySteps)
                tvStepsCount.text = "Today (Health Connect)"
                
                // Set placeholder data for other metrics (not available in this implementation)
                tvHeartRateValue.text = "--"
                tvHeartRateTime.text = "Not available"
                
                tvCaloriesValue.text = "--"
                tvCaloriesTime.text = "Not available"
                
                tvSleepValue.text = "--"
                tvSleepTime.text = "Not available"
                
                LogUtils.debug("Steps data loaded successfully: $todaySteps")
                
                LogUtils.debug("Successfully loaded $todaySteps steps from Health Connect")
                
                if (todaySteps == 0L) {
                    Toast.makeText(this@HealthVitalsActivity, "No steps recorded today. Start walking to see your data!", Toast.LENGTH_SHORT).show()
                }
                
            } catch (e: Exception) {
                LogUtils.debug("Error loading steps data: ${e.message}")
                showErrorMessage()
            }
        }
    }
    
    private fun showPermissionPrompt() {
        // Show no data until permissions are granted
        tvStepsValue.text = "--"
        tvStepsCount.text = "Permissions needed"
        
        tvHeartRateValue.text = "--"
        tvHeartRateTime.text = "Not available"
        
        tvCaloriesValue.text = "--"
        tvCaloriesTime.text = "Not available"
        
        tvSleepValue.text = "--"
        tvSleepTime.text = "Not available"
        
        btnGrantPermissions.visibility = View.VISIBLE
        tvNoData.text = "Grant Health Connect permissions to view your steps data"
        tvNoData.visibility = View.VISIBLE
    }
    
    private fun showNotSupportedMessage() {
        tvNoData.text = "Health Connect is not supported on this device"
        tvNoData.visibility = View.VISIBLE
        btnGrantPermissions.visibility = View.GONE
        hideAllDataCards()
    }
    
    private fun showNeedsUpdateMessage() {
        tvNoData.text = "Health Connect app needs to be updated. Please update from Play Store."
        tvNoData.visibility = View.VISIBLE
        btnGrantPermissions.visibility = View.GONE
        hideAllDataCards()
    }
    
    private fun showErrorMessage() {
        tvNoData.text = "Error loading health data. Please try again."
        tvNoData.visibility = View.VISIBLE
        hideAllDataCards()
    }
    
    private fun hideAllDataCards() {
        tvStepsValue.text = "--"
        tvStepsCount.text = "--"
        tvHeartRateValue.text = "--"
        tvHeartRateTime.text = "--"
        tvCaloriesValue.text = "--"
        tvCaloriesTime.text = "--"
        tvSleepValue.text = "--"
        tvSleepTime.text = "--"
    }
    
    private fun formatStepsCount(steps: Long): String {
        return when {
            steps >= 1000 -> {
                val thousands = steps / 1000.0
                if (thousands % 1 == 0.0) {
                    "${thousands.toInt()},${String.format("%03d", steps % 1000)}"
                } else {
                    steps.toString().replace("(\\d)(?=(\\d{3})+$)".toRegex(), "$1,")
                }
            }
            else -> steps.toString()
        }
    }
    
    override fun onResume() {
        super.onResume()
        // Refresh data when returning to the activity
        lifecycleScope.launch {
            if (healthConnectManager.hasStepsPermissions()) {
                loadStepsData()
            }
        }
    }
}