<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="BaudRate_Var">
        <item>9600</item>
        <item>19200</item>
        <item>115200</item>
    </string-array>
    <string-array name="IHBValue">
        <item>Normal</item>
        <item>IHB</item>
        <item>Tachycardia or Bradycardia</item>
        <item>Varied Heart Rate (±20%)</item>
        <item>Atrail Fibrillation (AF)</item>
    </string-array>
    <string-array name="abpm_types">
        <item>@string/abpm_general</item>
        <item>@string/abpm_daytime</item>
        <item>@string/abpm_nighttime</item>
        <item>@string/abpm_specialtime</item>
        <item>@string/abpm_backlight</item>
    </string-array>
    <integer-array name="audio_error_nums">
        <item>23</item>
        <item>24</item>
        <item>25</item>
        <item>26</item>
        <item>27</item>
        <item>28</item>
        <item>30</item>
        <item>40</item>
        <item>41</item>
        <item>42</item>
        <item>44</item>
        <item>46</item>
    </integer-array>
    <string-array name="audio_errors">
        <item>@string/err_23</item>
        <item>@string/err_24</item>
        <item>@string/err_25</item>
        <item>@string/err_26</item>
        <item>@string/err_27</item>
        <item>@string/err_28</item>
        <item>@string/err_30</item>
        <item>@string/err_40</item>
        <item>@string/err_41</item>
        <item>@string/err_42</item>
        <item>@string/err_44</item>
        <item>@string/err_46</item>
    </string-array>
    <string-array name="battery_status">
        <item>Not support</item>
        <item>Good</item>
        <item>Low</item>
        <item>Dead</item>
    </string-array>
    <array name="health_permissions">
        <item>androidx.health.permission.Steps.READ</item>
        <item>androidx.health.permission.Steps.WRITE</item>
        <item>androidx.health.permission.HeartRate.READ</item>
        <item>androidx.health.permission.HeartRate.WRITE</item>
        <item>androidx.health.permission.TotalCaloriesBurned.READ</item>
        <item>androidx.health.permission.TotalCaloriesBurned.WRITE</item>
        <item>androidx.health.permission.SleepSession.READ</item>
        <item>androidx.health.permission.SleepSession.WRITE</item>
    </array>
    <color name="App_color">#000000</color>
    <color name="background">#EFEFEF</color>
    <color name="black">#FF000000</color>
    <color name="black_overlay">#66000000</color>
    <color name="blue">#2196F3</color>
    <color name="cardColor">#35b736</color>
    <color name="colorAccent">#FF4081</color>
    <color name="colorPrimary">#3F51B5</color>
    <color name="colorPrimaryDark">#303F9F</color>
    <color name="colorText">#504F60</color>
    <color name="dark_blue">#1976D2</color>
    <color name="green">#00E676</color>
    <color name="grey">#cccccc</color>
    <color name="grey85">#d9d9d9</color>
    <color name="light_b">#18FFFF</color>
    <color name="light_blue">#007aff</color>
    <color name="light_grey">#BDBDBD</color>
    <color name="lightgrey">#D3D3D3</color>
    <color name="orange">#FFA726</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#AB47BC</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="purple_7000">#7B61FF</color>
    <color name="red">#EF5350</color>
    <color name="sky_blue">#82B1FF</color>
    <color name="status_blue">#24b4e4</color>
    <color name="statusbar">#FF3C9EF3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#EA80FC</color>
    <color name="textcardColor">#3f3f3f</color>
    <color name="transperant">#80000000</color>
    <color name="violet">#673fb4</color>
    <color name="white">#FFFFFF</color>
    <color name="white_smoke">#efefef</color>
    <color name="yellow">#FFFF00</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="app_bar_height">180dp</dimen>
    <dimen name="card_view_padding">4dp</dimen>
    <dimen name="d_10_d">10dp</dimen>
    <dimen name="d_10_s">15sp</dimen>
    <dimen name="d_12_d">12dp</dimen>
    <dimen name="d_15_d">15dp</dimen>
    <dimen name="d_20_d">20dp</dimen>
    <dimen name="d_20_s">20sp</dimen>
    <dimen name="d_25_d">25dp</dimen>
    <dimen name="d_25_s">25sp</dimen>
    <dimen name="d_30_d">30dp</dimen>
    <dimen name="d_30_s">30sp</dimen>
    <dimen name="d_35_s">35sp</dimen>
    <dimen name="d_3_d">3dp</dimen>
    <dimen name="d_50_d">50dp</dimen>
    <dimen name="d_5_d">5dp</dimen>
    <dimen name="d_7_d">10dp</dimen>
    <dimen name="dashboard_vital_text">20sp</dimen>
    <dimen name="date">40sp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="item_offset">4dp</dimen>
    <dimen name="margin_normal">10dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="message">35sp</dimen>
    <dimen name="message_text">16sp</dimen>
    <dimen name="padding_average">12dp</dimen>
    <dimen name="padding_empty_bottom">16dp</dimen>
    <dimen name="padding_medium">8dp</dimen>
    <dimen name="padding_normal">5dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="text_margin">16dp</dimen>
    <dimen name="time">60sp</dimen>
    <string name="BG">BG</string>
    <string name="BP">BP</string>
    <string name="Sp02">spO2</string>
    <string name="_0">0</string>
    <string name="_00_00">00:00</string>
    <string name="_0s">0s</string>
    <string name="_7278">7278</string>
    <string name="abpm_backlight">backlight</string>
    <string name="abpm_daytime">Day-time</string>
    <string name="abpm_dim">DIM</string>
    <string name="abpm_end_hour">End(hour)</string>
    <string name="abpm_general">General</string>
    <string name="abpm_get_config">get configuration</string>
    <string name="abpm_hours_of_study">Hours of Study</string>
    <string name="abpm_nighttime">Night-time</string>
    <string name="abpm_normal_period">Normal Period(min)</string>
    <string name="abpm_on_time">ON time(second)</string>
    <string name="abpm_period">Period(min)</string>
    <string name="abpm_specialtime">Special-time</string>
    <string name="abpm_start_after">Start after(min)</string>
    <string name="abpm_start_hour">Start(hour)</string>
    <string name="accept">Accept</string>
    <string name="accessibility_service_description">
        Smart installation service, you can automatically install the program without any user action. </string>
    <string name="action_settings">Settings</string>
    <string name="add">Add</string>
    <string name="add_new_dairy">Patient Diary</string>
    <string name="addnewdairy">addNewDiary</string>
    <string name="after_meter_system_clock">After Sync Meter Time:</string>
    <string name="afternoon">Afternoon Snack</string>
    <string name="age_value">Age:</string>
    <string name="alerts">Alerts (Last 2 Days)</string>
    <string name="all">All</string>
    <string name="already_validated"><u>Login</u></string>
    <string name="answer">answer</string>
    <string name="app_logo">App Logo</string>
    <string name="app_name">WatchRx Health</string>
    <string name="app_version">App version 1.0</string>
    <string name="appointment">Appointments</string>
    <string name="assistance">Assistance</string>
    <string name="audio_device">Audio Device</string>
    <string name="available_devices">Available Devices</string>
    <string name="back_home">You are back home</string>
    <string name="battery_status">Battery Status</string>
    <string name="baudrate">BaudRate</string>
    <string name="bed">Bed</string>
    <string name="before_meter_system_clock">Before Sync Meter Time:</string>
    <string name="bf_value">BF:</string>
    <string name="bg_value">BG(mg/dL):</string>
    <string name="blood_pressure">Blood Pressure</string>
    <string name="bluetooth">Bluetooth</string>
    <string name="bluetooth_need_to_pair"><![CDATA[Please go to "Settings—>Wireless & networks" pair Bluetooth devices]]></string>
    <string name="bluetooth_occur_error">Bluetooth occur error,please enable bluetooth and pair remot devices</string>
    <string name="bmi_value">BMI:</string>
    <string name="bmr_value">BMR:</string>
    <string name="breakfast">Breakfast</string>
    <string name="browse">Browse</string>
    <string name="bt_type_one">Connect</string>
    <string name="bt_type_two">Listen</string>
    <string name="calling">calling</string>
    <string name="calories_burned">Calories Burned</string>
    <string name="cancel">Cancel</string>
    <string name="care_manager">Care Manager</string>
    <string name="cd_image">image</string>
    <string name="charge_me">Charge Me!</string>
    <string name="chat_with_care_team">Chat with care team</string>
    <string name="check_bt_fail">device singal to low!!!!</string>
    <string name="choose">Choose Image</string>
    <string name="clear_records">Meter records were cleaned.</string>
    <string name="cm_name">Care Manager Name</string>
    <string name="cm_phone">Phone</string>
    <string name="codec_title">Set your preferred audio and video codec. Not all codecs are supported with Group rooms. The media server will fallback to OPUS or VP8 if a preferred codec is not supported.</string>
    <string name="color">Color</string>
    <string name="command_test_menu">Choose One Command Type</string>
    <string name="common_back">Back</string>
    <string name="common_cancel">Cancel</string>
    <string name="common_confirm">Confirm</string>
    <string name="common_connect_internet_first">Please connect to the Internet first.</string>
    <string name="common_delete">Delete</string>
    <string name="common_error">Error</string>
    <string name="common_export">Export</string>
    <string name="common_max">Max.</string>
    <string name="common_message">Message</string>
    <string name="common_min">Min.</string>
    <string name="common_no">No</string>
    <string name="common_off">off</string>
    <string name="common_ok">OK</string>
    <string name="common_on">on</string>
    <string name="common_save_and_leave">Save and leave?</string>
    <string name="common_warning">Warning</string>
    <string name="common_yes">Yes</string>
    <string name="confirm">Confirm</string>
    <string name="connect">CONFIRM</string>
    <string name="connect_4026">Connect TD4026</string>
    <string name="connect_me">Connect Me !!</string>
    <string name="connect_meter">Connect Meter</string>
    <string name="connect_meter_fail">Connect meter fail, Please Check the Bluetooth module, and retry!!</string>
    <string name="connect_meter_success">Connect meter success, you can choose one of the command to test meter communication!!</string>
    <string name="connect_wifi">Refresh</string>
    <string name="connection_meter_and_get_result">Start connecting meter and get result, please wait a while</string>
    <string name="custom_reply">Custom Reply</string>
    <string name="daily_notifications">Daily notifications</string>
    <string name="dairy">Patient Diary</string>
    <string name="data_not_correct_error">Calibration data not correct.</string>
    <string name="date">Date</string>
    <string name="days_to_take">Days to Take:</string>
    <string name="default_notification_channel_id" translatable="false">fcm_default_channel</string>
    <string name="default_web_client_id" translatable="false">132597499285-j5595fr9kvpgutlfhaapv4bkv2rk24ij.apps.googleusercontent.com</string>
    <string name="delete">Delete</string>
    <string name="device_list">Device List:</string>
    <string name="device_mac_address">Device Mac Address:</string>
    <string name="device_name">Device Name:</string>
    <string name="dia_value">Diastolic(mmHg):</string>
    <string name="dinner">Dinner</string>
    <string name="disable_alwayson_fail">Disable function fail!</string>
    <string name="disable_alwayson_success">Disable function success!</string>
    <string name="disconnected">Device Disconnected</string>
    <string name="dosage">Dosage</string>
    <string name="dummy_button">Dummy Button</string>
    <string name="dummy_content">DUMMY\nCONTENT</string>
    <string name="early_morning">Early Morning</string>
    <string name="email_id">Enter Email Id Or Phone</string>
    <string name="emergency_contact">Emergency Contact</string>
    <string name="enable_health_connect">Connect Health Apps</string>
    <string name="enter_meal_taken_time">Enter Meal Taken Time</string>
    <string name="enter_medication_taken_time">Enter Medication Taken Time</string>
    <string name="enter_message">Type a message</string>
    <string name="enter_your_message_here">Enter your message here</string>
    <string name="err_23">Internal memory data incorrect or system calibration is not finished.</string>
    <string name="err_24">User inserts used strip.</string>
    <string name="err_25">Ambient temperature is less than the limitation.(Depend on code parameter)(10℃)</string>
    <string name="err_26">Ambient temperature is more than the limitation.(Depend on code parameter)(40℃)</string>
    <string name="err_27">Expiry Date_Error (HCT Only)</string>
    <string name="err_28">Code Strip data is incorrect.</string>
    <string name="err_30">Rcode Out of Range</string>
    <string name="err_40">User remove the strip when measuring blood glucose.</string>
    <string name="err_41">Internal EEprom not response ACK.</string>
    <string name="err_42">Battery Low &lt; 3.0v!!</string>
    <string name="err_44">User cancel the glucose measuring.</string>
    <string name="err_46">HCT value is incorrect. (HCT Only)</string>
    <string name="error_location_not_supported">Please grant location access so this app can detect beacons.</string>
    <string name="error_location_not_supported2">Since location access has not been granted, this app will not be able to discover beacons when in the background.</string>
    <string name="error_retrieving_access_token">Error retrieving token</string>
    <string name="exit">EXIT</string>
    <string name="family_icon">family_icon</string>
    <string name="far_home">You have come too far from home</string>
    <string name="fence_crossed">Fence Crossed</string>
    <string name="firebase_database_url" translatable="false">https://watchrx-1007.firebaseio.com</string>
    <string name="forgot_your_password"><u>Forgot your password?</u></string>
    <string name="freq">Frequency(Time-Quantity):</string>
    <string name="fri">Fri</string>
    <string name="gcmRegFailedMsg">GCM Registration failed. This device does not support for Google Play Service!</string>
    <string name="gcmRegFailedNoPlayServiceMsg">GCM Registration failed. Google Play Service is not installed/enabled in this device!</string>
    <string name="gcm_defaultSenderId" translatable="false">132597499285</string>
    <string name="gcm_registration_failed">GCM token Registration with server failed.</string>
    <string name="gender_value">Gender:</string>
    <string name="geofence_transition_entered">Entered</string>
    <string name="geofence_transition_exited">Exited</string>
    <string name="get_battery_status">Get Battery Status</string>
    <string name="get_measurement_record">Measurement Record</string>
    <string name="get_meter_system_clock">Get Meter System Clock</string>
    <string name="get_project_code">Get Project Code</string>
    <string name="get_serial_number">Get Serial Number</string>
    <string name="get_storage_count">Get Storage Count</string>
    <string name="google_api_key" translatable="false">AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4</string>
    <string name="google_app_id" translatable="false">1:132597499285:android:ebc4577d313cba0519e29c</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4</string>
    <string name="google_storage_bucket" translatable="false">watchrx-1007.appspot.com</string>
    <string name="gps_status">GPS Status</string>
    <string name="have_you_taken_medications">HAVE YOU TAKEN MEDICATIONS?</string>
    <string name="have_you_taken_meds">Have you taken meds?</string>
    <string name="health_connect_data">Health &amp; Fitness Data</string>
    <string name="health_connect_permission_needed">Health Connect integration coming soon</string>
    <string name="health_manage">Health Manager</string>
    <string name="health_overview">Health overview</string>
    <string name="health_vitals">Health Vitals</string>
    <string name="heart_rate">Heart Rate</string>
    <string name="height_value">Height:</string>
    <string name="hint">Please choose one pair meter in list</string>
    <string name="how_is_your_health">How is your health?</string>
    <string name="ihb_value">IHB:</string>
    <string name="imei_not_assigned">Imei is not associated with any patient</string>
    <string name="imei_not_found_in_db">Imei is not found in the database</string>
    <string name="incoming_call">Incoming Call</string>
    <string name="inhaler">Inhaler</string>
    <string name="injection">Injection</string>
    <string name="instruction">Please switch on your medical device to begin health data collection. Let us know if you need any help!</string>
    <string name="internetcheck">Internet is not connected ! please connect</string>
    <string name="knv_age">Age</string>
    <string name="knv_back">Back</string>
    <string name="knv_bfr">Body Fat Ratio</string>
    <string name="knv_bm">Basal Metabolism</string>
    <string name="knv_gender">Gender</string>
    <string name="knv_height">Height</string>
    <string name="knv_mc">Moisture Content</string>
    <string name="knv_mr">Muscle Ratio</string>
    <string name="knv_sw">Skeleton Weight</string>
    <string name="knv_user">User</string>
    <string name="knv_weight">Weight</string>
    <string name="last_two_days_alerts">Last two days alerts</string>
    <string name="loading">Loading...</string>
    <string name="login">Login</string>
    <string name="logout">Logout</string>
    <string name="low_battery_error">Battery is too low.</string>
    <string name="lunch">Lunch</string>
    <string name="manage_prescriptions">Manage prescriptions</string>
    <string name="max_audio_bitrate">Max Audio Bitrate (bits per second)</string>
    <string name="max_video_bitrate">Max Video Bitrate (bits per second)</string>
    <string name="measure_error">Blood Pressure Measurement Error.</string>
    <string name="measurement_date">Measurement Date:</string>
    <string name="measurement_type">Measurement Type:</string>
    <string name="medication">+Medication</string>
    <string name="medication_icon">Medication Icon</string>
    <string name="medication_name">Medication Name</string>
    <string name="medicationimage">MedicationImage</string>
    <string name="medications">Medications</string>
    <string name="meds_type">Medication Type</string>
    <string name="message">Message</string>
    <string name="message_icon">Message Icon</string>
    <string name="messages">Messages</string>
    <string name="meter_bt_transfer_type">Meter Bluetooth Transfer Type</string>
    <string name="meter_search">Search</string>
    <string name="meter_system_clock">Meter Time:</string>
    <string name="meter_trans_type_fail">Please choose meter transfer type!!!!</string>
    <string name="meter_was_power_off">Meter was power off, click OK and go to the meter menu.</string>
    <string name="mon">Mon</string>
    <string name="monday">Mon</string>
    <string name="mute">Mute</string>
    <string name="my_vitals">My Vitals</string>
    <string name="nav_alerts">Alerts</string>
    <string name="nav_contacts">Contacts</string>
    <string name="nav_sound">Support</string>
    <string name="nfc_disable">NFC disable</string>
    <string name="nfc_listen">NFC listening</string>
    <string name="nfc_read">NFC reading</string>
    <string name="nfc_read_timeout">Read NFC timeout</string>
    <string name="no">No</string>
    <string name="no_alerts_in_the_last_two_days">No alerts in the last two days</string>
    <string name="no_data_found">No data found.</string>
    <string name="no_device">No Device</string>
    <string name="no_device_f">No device found.</string>
    <string name="no_device_mesg">You have not connected any device…</string>
    <string name="no_health_data">No health data available</string>
    <string name="not_connect_serial_port">Not connect serial port!!!!</string>
    <string name="not_spport">Device wont support</string>
    <string name="not_support_meter">Not support meter!!!!</string>
    <string name="nurseIsRemindingText">Your Nurse is reminding you</string>
    <string name="nurse_is_reminding_you">Nurse is reminding you</string>
    <string name="ok">OK</string>
    <string name="one_time">One Time</string>
    <string name="otp">Enter OTP</string>
    <string name="otp_validate"><u>Validate OTP</u></string>
    <string name="over_error">Inflation or Pressure Over Error.</string>
    <string name="p_name">Patient Name</string>
    <string name="pair_meter_first">Please Pair Device First!!</string>
    <string name="pair_result">Pair Device Result</string>
    <string name="password">Enter Password</string>
    <string name="password_hint">Password must contain 8–20 characters, including a number, a special character, a lowercase and an uppercase letter.</string>
    <string name="pedometer">Pedometer</string>
    <string name="permissions_needed">Camera and Microphone permissions needed. Please allow in App Settings for additional functionality.</string>
    <string name="plz">Please wait…</string>
    <string name="power_off">Power off</string>
    <string name="power_on">Power on</string>
    <string name="pref_title_audio_codec">Audio Codec</string>
    <string name="pref_title_video_codec">Video Codec</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="project_code">Project Code:</string>
    <string name="project_id" translatable="false">watchrx-1007</string>
    <string name="provider_name">Provider Name</string>
    <string name="provider_phone">Phone</string>
    <string name="pul_value">Pulse(BPM):</string>
    <string name="quality_good">Quality: Good</string>
    <string name="quantity">Quantity</string>
    <string name="real_time">Real Time</string>
    <string name="reject">Reject</string>
    <string name="remind_again">REMIND AGAIN</string>
    <string name="remind_me_again">Remind Me Again</string>
    <string name="reminder">Reminder</string>
    <string name="reminders">Reminders</string>
    <string name="remindertext">REMINDER</string>
    <string name="resendOTP">Resend OTP</string>
    <string name="retry">Retry</string>
    <string name="rom_not_resp_error">Internal EEprom not response ACK.</string>
    <string name="room_screen_select_device">Select Device</string>
    <string name="room_status">status</string>
    <string name="sat">Sat</string>
    <string name="saveandleave">Save and leave?</string>
    <string name="scan">SCAN</string>
    <string name="scanning">Scanning…</string>
    <string name="schedule_visits">Schedule visits</string>
    <string name="select_days">Select Days</string>
    <string name="select_option">Select Option</string>
    <string name="select_timeslots">Select Timeslots</string>
    <string name="send">Send</string>
    <string name="sender_bandwidth_constraints">Set sender bandwidth constraints. Zero represents the WebRTC default value which varies by codec.</string>
    <string name="serial_number">Serial Number:</string>
    <string name="set_meter_system_clock">Set Meter System Clock</string>
    <string name="setting_available_meter">Available Meters</string>
    <string name="setting_current_ble_meter">Current Meters</string>
    <string name="setting_home_meter">Bluetooth Smart Meter</string>
    <string name="setting_meter_stop">Stop</string>
    <string name="settings">Settings</string>
    <string name="sleep_end_time">Sleep End Time :</string>
    <string name="sleep_hours">Sleep Hours</string>
    <string name="sleep_start_time">Sleep Start Time :</string>
    <string name="snoozetext">SNOOZE</string>
    <string name="spO2_value">SpO2:</string>
    <string name="speaker">Speaker</string>
    <string name="spray">Nosal Spray</string>
    <string name="start_search_meter">Start searching meters,please wait a while</string>
    <string name="state">State</string>
    <string name="statusTitle">Connecting…</string>
    <string name="steps_today">Steps Today</string>
    <string name="storage_count">Total Records:</string>
    <string name="submit">Submit</string>
    <string name="subscription_not_active">Subscription is not active</string>
    <string name="summary">Summary</string>
    <string name="sun">Sun</string>
    <string name="sunday">Sun</string>
    <string name="syrup">Syrup</string>
    <string name="sys_value">Systolic(mmHg):</string>
    <string name="tab_setting">SETTING</string>
    <string name="tablet">Tablet</string>
    <string name="take">Take</string>
    <string name="take_me_out">Take Me Out!</string>
    <string name="temp_c">Temperature(°C):</string>
    <string name="temp_f">Temperature(°F):</string>
    <string name="thermometer">Thermometer</string>
    <string name="thermometer_value">Body Temperature(°C):</string>
    <string name="thur">Thur</string>
    <string name="thursday">Thu</string>
    <string name="title_activity_fullscreen">FullscreenActivity</string>
    <string name="title_activity_patient_diary">ScrollingActivity</string>
    <string name="title_activity_settings">Settings</string>
    <string name="title_activity_splash">WatchRx</string>
    <string name="today_s_steps">Today Steps</string>
    <string name="todo">Mute/Un-mute</string>
    <string name="token_failed">Failed to get auth token</string>
    <string name="track_daily_activities">Track daily activities</string>
    <string name="track_health_metrics">Track health metrics</string>
    <string name="tue">Tue</string>
    <string name="tuesday">Tue</string>
    <string name="turn_off">Turn off meter</string>
    <string name="unknow_error">Unknown error!!!</string>
    <string name="unknown_geofence_transition">Unknown Transition</string>
    <string name="update_settings">Update Settings</string>
    <string name="user_respond_text">Please select any option to respond it.</string>
    <string name="user_respond_text_no_answer_text">Please tap anywhere to respond it.</string>
    <string name="username_hint">Please enter valid email or phone number with country code (Example:+18001232323)</string>
    <string name="validateotp">Validate OTP</string>
    <string name="value">Value</string>
    <string name="view_device">View Device</string>
    <string name="vital_config">My Vitals</string>
    <string name="vital_icon">Vital Icon</string>
    <string name="vital_name">Vital name</string>
    <string name="vitals">Vitals</string>
    <string name="vp8_simulcast">VP8 Simulcast</string>
    <string name="wait">Please Wait I am reading Data :-) </string>
    <string name="watchRegFailedMsg">Watch registration rejected by server.either IMEI is not registered or parent is not assigned to this watch.</string>
    <string name="watchrx">WatchRx</string>
    <string name="wc">Weight Scale</string>
    <string name="wed">Wed</string>
    <string name="wednesday">Wed</string>
    <string name="weight_kg">Weight(Kg):</string>
    <string name="weight_lbs">Weight(lbs):</string>
    <string name="weight_value">Weight(lbs):</string>
    <string name="yes">Yes</string>
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="AppTheme1" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/transperant</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="CircleImage" parent="">
        <item name="cornerSize">50%</item>
    </style>
    <style name="CustomProgressDialog" parent="Theme.AppCompat.Dialog">
        
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style>
    <style name="FullscreenActionBarStyle" parent="Widget.AppCompat.ActionBar">
        <item name="android:background">@color/black_overlay</item>
    </style>
    <style name="FullscreenTheme" parent="AppTheme">
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="metaButtonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="metaButtonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
    </style>
    <style name="PasswordCriteria">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#FF0000</item>
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/lato_regular</item>
        <item name="android:paddingBottom">2dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.App.rounded" parent="">
        <item name="cornerSize">50%</item>
    </style>
    <style name="Theme.WatchRx" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/blue</item>
        <item name="colorPrimaryVariant">@color/dark_blue</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">@color/blue</item>
    </style>
    <style name="Theme.WatchRx.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.WatchRx.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.WatchRx.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="UiTestTextView">
        <item name="android:textColor">#ff0000</item>
        <item name="android:textSize">16sp</item>

        <item name="android:shadowColor">#ffffff</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">3</item>
    </style>
    <style name="cancelButton" parent="@android:style/Widget.Button">
        <item name="android:background">#FF5722</item>
    </style>
    <style name="loginButton" parent="@android:style/Widget.Button">
        <item name="android:background">#8BC34A</item>
    </style>
    <declare-styleable name="ButtonBarContainerTheme">
        <attr format="reference" name="metaButtonBarStyle"/>
        <attr format="reference" name="metaButtonBarButtonStyle"/>
    </declare-styleable>
</resources>