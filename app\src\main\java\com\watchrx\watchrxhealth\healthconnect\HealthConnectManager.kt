package com.watchrx.watchrxhealth.healthconnect

import android.content.Context
import android.util.Log
import androidx.health.connect.client.HealthConnectClient
import androidx.health.connect.client.PermissionController
import androidx.health.connect.client.aggregate.AggregationResult
import androidx.health.connect.client.permission.HealthPermission
import androidx.health.connect.client.records.StepsRecord
import androidx.health.connect.client.request.AggregateRequest
import androidx.health.connect.client.request.ReadRecordsRequest
import androidx.health.connect.client.time.TimeRangeFilter
import com.watchrx.watchrxhealth.utils.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset

class HealthConnectManager(private val context: Context) {
    
    companion object {
        private const val TAG = "HealthConnectManager"
        const val HEALTH_CONNECT_PACKAGE_NAME = "com.google.android.apps.healthdata"
    }
    
    private var healthConnectClient: HealthConnectClient? = null

    fun checkHealthConnectAvailability(): HealthConnectAvailability {
        return when (HealthConnectClient.getSdkStatus(context, HEALTH_CONNECT_PACKAGE_NAME)) {
            HealthConnectClient.SDK_UNAVAILABLE -> {
                LogUtils.debug("Health Connect SDK is unavailable")
                HealthConnectAvailability.NOT_SUPPORTED
            }
            HealthConnectClient.SDK_UNAVAILABLE_PROVIDER_UPDATE_REQUIRED -> {
                LogUtils.debug("Health Connect app needs to be updated")
                HealthConnectAvailability.NEEDS_UPDATE
            }
            else -> {
                LogUtils.debug("Health Connect is available")
                HealthConnectAvailability.AVAILABLE
            }
        }
    }

    private fun getHealthConnectClient(): HealthConnectClient {
        if (healthConnectClient == null) {
            healthConnectClient = HealthConnectClient.getOrCreate(context)
        }
        return healthConnectClient!!
    }

    fun getStepsPermissions(): Set<String> {
        return setOf(
            HealthPermission.getReadPermission(StepsRecord::class),
            HealthPermission.getWritePermission(StepsRecord::class)
        )
    }

    suspend fun hasStepsPermissions(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val grantedPermissions = client.permissionController.getGrantedPermissions()
                val requiredPermissions = getStepsPermissions()
                LogUtils.debug("Granted permissions: $grantedPermissions")
                LogUtils.debug("Required permissions: $requiredPermissions")
                grantedPermissions.containsAll(requiredPermissions)
            } catch (e: Exception) {
                Log.e(TAG, "Error checking permissions", e)
                false
            }
        }
    }

    fun createPermissionRequestContract() = PermissionController.createRequestPermissionResultContract()

    suspend fun getTodaySteps(): Long {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                
                val today = LocalDate.now()
                val startOfDay = today.atStartOfDay(ZoneId.systemDefault()).toInstant()
                val endOfDay = today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()
                
                LogUtils.debug("Reading steps from $startOfDay to $endOfDay")
                
                // Create aggregation request for steps
                val request = AggregateRequest(
                    metrics = setOf(StepsRecord.COUNT_TOTAL),
                    timeRangeFilter = TimeRangeFilter.between(startOfDay, endOfDay)
                )
                
                val response = client.aggregate(request)
                val totalSteps = response[StepsRecord.COUNT_TOTAL] ?: 0L
                
                LogUtils.debug("Total steps for today: $totalSteps")
                totalSteps
                
            } catch (e: Exception) {
                Log.e(TAG, "Error reading steps data", e)
                0L
            }
        }
    }

    suspend fun getStepsInRange(startTime: Instant, endTime: Instant): List<StepData> {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                
                val request = ReadRecordsRequest(
                    recordType = StepsRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )
                
                val response = client.readRecords(request)
                val stepDataList = response.records.map { record ->
                    StepData(
                        count = record.count,
                        startTime = record.startTime,
                        endTime = record.endTime
                    )
                }
                
                LogUtils.debug("Found ${stepDataList.size} step records")
                stepDataList
                
            } catch (e: Exception) {
                Log.e(TAG, "Error reading steps range data", e)
                emptyList()
            }
        }
    }

    suspend fun writeStepsData(count: Long, startTime: Instant, endTime: Instant): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                
                val stepsRecord = StepsRecord(
                    count = count,
                    startTime = startTime,
                    endTime = endTime,
                    startZoneOffset = ZoneOffset.systemDefault().rules.getOffset(startTime),
                    endZoneOffset = ZoneOffset.systemDefault().rules.getOffset(endTime)
                )
                
                client.insertRecords(listOf(stepsRecord))
                LogUtils.debug("Successfully wrote $count steps from $startTime to $endTime")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error writing steps data", e)
                false
            }
        }
    }

    data class StepData(
        val count: Long,
        val startTime: Instant,
        val endTime: Instant
    )

    enum class HealthConnectAvailability {
        AVAILABLE,
        NOT_SUPPORTED,
        NEEDS_UPDATE
    }
}