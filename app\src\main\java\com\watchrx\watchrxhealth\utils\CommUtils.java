package com.watchrx.watchrxhealth.utils;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Environment;
import android.util.Log;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.Alerts;
import com.watchrx.watchrxhealth.db.MedicationScheduleInstance;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.ServerQueue;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.messages.AcknowledgeReminder;
import com.watchrx.watchrxhealth.messages.ReminderDisplayedLogMessage;
import com.watchrx.watchrxhealth.receivers.BleBroadCastReceiver;
import com.watchrx.watchrxhealth.receivers.CheckQueueReceiver;
import com.watchrx.watchrxhealth.receivers.CustomAlertReceiver;
import com.watchrx.watchrxhealth.receivers.InternetStatusChangeReceiver;
import com.watchrx.watchrxhealth.receivers.MidnightPedoMeterResetReceiver;
import com.watchrx.watchrxhealth.receivers.ReminderReceiver;
import com.watchrx.watchrxhealth.receivers.ScheduleMessageReceiver;
import com.watchrx.watchrxhealth.receivers.SendPedoMeterReceiver;
import com.watchrx.watchrxhealth.receivers.TimeZoneChangedReceiver;
import com.watchrx.watchrxhealth.receivers.VitalReminderReceiver;
import com.watchrx.watchrxhealth.syncup.MedicationScheduleSetupReceiver;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

public class CommUtils {

    private static ArrayList<String> listOfDevices;


    public static void AddRoundDialDevicesToList() {
        ArrayList<String> ListOfRoundDialDevices = new ArrayList<>();
        ListOfRoundDialDevices.add("Alps S99");
        ListOfRoundDialDevices.add("Alps S216");
        ListOfRoundDialDevices.add("Alps LEM7");
        ListOfRoundDialDevices.add("Alps LEM9");
//        ListOfRoundDialDevices.add("Xiaomi Redmi Note 7S");
        ListOfRoundDialDevices.add("Alps Z30");
        ListOfRoundDialDevices.add("Alps Z28(Z29)");
        ListOfRoundDialDevices.add("Alps LEM12");
        listOfDevices = ListOfRoundDialDevices;
    }

    public static HashMap<String, String> getMedicalDeviceToList() {
        HashMap<String, String> hash_map = new HashMap<>();
        hash_map.put("TAIDOC TD1242", "Temperature");
        hash_map.put("TAIDOC TD8255", "Oxygen Saturation");
        hash_map.put("TAIDOC TD4277", "Blood Sugar");
        hash_map.put("TAIDOC TD3128", "Blood Pressure");
        hash_map.put("TAIDOC TD2555", "Weight");
        return hash_map;
    }

    public static String getMedicalDeviceType(String vitalType) {
        if (vitalType != null) {
            if (vitalType.equalsIgnoreCase("Temperature")) {
                return "TAIDOC TD1242";
            } else if (vitalType.equalsIgnoreCase("Oxygen Saturation")) {
                return "TAIDOC TD8255";
            } else if (vitalType.equalsIgnoreCase("Blood Sugar")) {
                return "TAIDOC TD4277";
            } else if (vitalType.equalsIgnoreCase("Blood Pressure")) {
                return "TAIDOC TD3128";
            } else if (vitalType.equalsIgnoreCase("Weight")) {
                return "TAIDOC TD2555";
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    public static ArrayList<String> getBleDeviceList() {
        ArrayList<String> listOfDevice = new ArrayList<>();
        listOfDevice.add("T4");
        listOfDevice.add("I12");
        listOfDevice.add("Watchrx12");
        listOfDevice.add("WatchRx 12");
        listOfDevice.add("Watchrx4");
        return listOfDevice;
    }

    public static ArrayList<String> getRoundDialDevices() {
        return listOfDevices;
    }

    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        // make it false

        NetworkInfo networkInfo = mConnectivityManager.getActiveNetworkInfo();

        if (networkInfo != null && networkInfo.isAvailable() && networkInfo.isConnected()) {
            String networkType = networkInfo.getTypeName();
            LogUtils.debug("While checking,Internet is connected to ===>" + networkType);
            return true;
        } else {
            return false;
        }

    }

    public static void printTraceToLogFile(Exception e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw));
        String stackTrace = sw.toString();
        LogUtils.debug(stackTrace);
    }

    public static void pushToServer(Context context, String jsonMsg, String url) {
        LogUtils.debug("Queuing for delivery to server: \n\t- URL: " + url + "\n\t- json: " + jsonMsg);
        ServerQueue.addToDB(jsonMsg, url);

        // intimate the writer background service
        Intent serverCommRequestedIntent = new Intent(MainActivity.SERVER_COMM_INITIATED_INDICATOR_INTENT_FILTER);
        LocalBroadcastManager.getInstance(context).sendBroadcast(serverCommRequestedIntent);
    }

    static String getTodayDate() {
        return new SimpleDateFormat("yyyy-MM-dd", Locale.US).format(new Date(System.currentTimeMillis()));
    }

    static String getCurrentTime() {
        return new SimpleDateFormat("HH:mm:ss", Locale.US).format(new Date(System.currentTimeMillis()));
    }

    public static void acknowledgeReminderLogToServer(Context context, String alertDescription, String alertType,
                                                      String beforeOrAfterFood, String missedMedicationIds, String missedTimeSlot, String pId, String alertId) {

        AcknowledgeReminder reminderMessage = new AcknowledgeReminder();
        reminderMessage.alertId = alertId;
        reminderMessage.alertDescription = alertDescription;
        reminderMessage.alertType = alertType;
        reminderMessage.createdDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis()));
        reminderMessage.missedBeforeOrAfterFood = beforeOrAfterFood;
        reminderMessage.missedMedicationIds = missedMedicationIds;
        reminderMessage.missedTime = new SimpleDateFormat("HH:mm:ss", Locale.US).format(new Date(System.currentTimeMillis()));
        reminderMessage.missedTimeSlot = missedTimeSlot;
        reminderMessage.patientId = pId;
        CommUtils.pushToServer(context, new Gson().toJson(reminderMessage), URLConstants.MISSED_MEDICATION_URL);
    }

    public static void sendReminderDisplayedLogToServer(Context context, String alertType, String alertId, String beforeOrAfterFood,
                                                        String timeSlot, List<MedicationScheduleInstance> scheduleInstanceList) {

        LogUtils.debug("Forming log to send to server.");
        ReminderDisplayedLogMessage logMessage = new ReminderDisplayedLogMessage();
        PatientDetails patientDetails = PatientDetails.getFromDB();
        boolean firstTime = true;
        StringBuilder medicationIds = new StringBuilder();
        StringBuilder medicationNames = new StringBuilder();
        //StringBuilder fixedMedicineTime = new StringBuilder();
        String fixName = "";
        for (MedicationScheduleInstance scheduleInstance : scheduleInstanceList) {
            if (!firstTime) {
                medicationIds.append(",");
                medicationNames.append(",");
                //fixedMedicineTime.append(",");
            } else {
                firstTime = false;
            }

            medicationIds.append(scheduleInstance.getMedicineID());
            //fixedMedicineTime.append(scheduleInstance.getTimeSlot());
            fixName = scheduleInstance.getTimeSlot();
            MedicationScheduleMaster medicine = MedicationScheduleMaster.getForMedicineId(scheduleInstance.getMedicineID());
            if (medicine != null) {
                medicationNames.append(medicine.getMedicineName());
            }
        }
        if (beforeOrAfterFood.equalsIgnoreCase("Fixed")) {
            timeSlot = "Fixed";
        }
        String tempAlertType = alertType;
        if (alertType.equalsIgnoreCase("MissedRegularReminder")) {
            tempAlertType = "Missed Regular Reminder";
        } else if (alertType.equalsIgnoreCase("MissedNurseReminder")) {
            tempAlertType = "Missed Nurse Reminder";
        }
        logMessage.alertId = alertId;
        logMessage.missedTime = new SimpleDateFormat("HH:mm:ss", Locale.US).format(new Date(System.currentTimeMillis()));
        logMessage.patientId = patientDetails.getPatientId();
        logMessage.missedTimeSlot = fixName;
        logMessage.missedBeforeOrAfterFood = beforeOrAfterFood;
        logMessage.missedMedicationIds = medicationIds.toString();
        logMessage.alertType = tempAlertType;
        logMessage.alertDescription = medicationNames.toString();
        logMessage.createdDate = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis()));

        LogUtils.debug("Log Formed. Will push to server.");

        CommUtils.pushToServer(context, new Gson().toJson(logMessage), URLConstants.MISSED_MEDICATION_URL);
        if (alertType.equalsIgnoreCase("MissedRegularReminder")) {
            if (timeSlot.equalsIgnoreCase("Fixed")) {
                LogUtils.debug("Adding alert to alerts table for " + alertType);
                addLogsToAlertsTable(context, alertType, getTimein12format(fixName), medicationNames.toString());
            } else {
                LogUtils.debug("Adding alert to alerts table for " + alertType);
                addLogsToAlertsTable(context, alertType, timeSlot, medicationNames.toString());
            }
        } else if (alertType.equalsIgnoreCase("MissedNurseReminder")) {
            if (timeSlot.equalsIgnoreCase("Fixed")) {
                LogUtils.debug("Adding alert to alerts table for " + alertType);
                addLogsToAlertsTable(context, alertType, getTimein12format(fixName), medicationNames.toString());
            } else {
                LogUtils.debug("Adding alert to alerts table for " + alertType);
                addLogsToAlertsTable(context, alertType, timeSlot, medicationNames.toString());
            }
        }
    }

    private static String getTimein12format(String time) {
        try {
            final SimpleDateFormat sdf = new SimpleDateFormat("H:mm", Locale.US);
            final Date dateObj = sdf.parse(time);
            return new SimpleDateFormat("hh:mm aa", Locale.US).format(dateObj);
        } catch (final ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    private static void addLogsToAlertsTable(Context context, String alertType, String timeSlot, String medicineName) {
        List<Alerts> alert_count = Alerts.getFromDB();
        if (alert_count.size() != 0) {
            if (containsAlerts(alert_count, "MissedRegularReminder")) {
                Alerts.deleteRowAlert("MissedRegularReminder");
                Alerts alerts = new Alerts();
                alerts.setAlertType(alertType);
                alerts.setAlertMessage(timeSlot);
                alerts.setAlertDate(new SimpleDateFormat("MMM dd, hh:mm:ss a", Locale.US).format(new Date(System.currentTimeMillis())));
                alerts.setMedicineName(medicineName);
                Alerts.addToDB(alerts);
                Intent alertCount = new Intent(MainActivity.ALERT_COUNT_INCREASED_INDICATOR);
                LocalBroadcastManager.getInstance(context).sendBroadcast(alertCount);
            } else if (containsAlerts(alert_count, "MissedNurseReminder")) {
                Alerts.deleteRowAlert("MissedNurseReminder");
                Alerts alerts = new Alerts();
                alerts.setAlertType(alertType);
                alerts.setAlertMessage(timeSlot);
                alerts.setAlertDate(new SimpleDateFormat("MMM dd, hh:mm:ss a", Locale.US).format(new Date(System.currentTimeMillis())));
                alerts.setMedicineName(medicineName);
                Alerts.addToDB(alerts);
                Intent alertCount = new Intent(MainActivity.ALERT_COUNT_INCREASED_INDICATOR);
                LocalBroadcastManager.getInstance(context).sendBroadcast(alertCount);
            } else {
                Alerts alerts = new Alerts();
                alerts.setAlertType(alertType);
                alerts.setAlertMessage(timeSlot);
                alerts.setAlertDate(new SimpleDateFormat("MMM dd, hh:mm:ss a", Locale.US).format(new Date(System.currentTimeMillis())));
                alerts.setMedicineName(medicineName);
                Alerts.addToDB(alerts);
                Intent alertCount = new Intent(MainActivity.ALERT_COUNT_INCREASED_INDICATOR);
                LocalBroadcastManager.getInstance(context).sendBroadcast(alertCount);
            }
        } else {
            if (alertType.equalsIgnoreCase("MissedRegularReminder") || alertType.equalsIgnoreCase("MissedNurseReminder")) {
                LogUtils.debug("Adding missed logs to alerts list " + alertType + "-" + timeSlot);
                Alerts alerts = new Alerts();
                alerts.setAlertType(alertType);
                alerts.setAlertMessage(timeSlot);
                alerts.setAlertDate(new SimpleDateFormat("MMM dd, hh:mm:ss a", Locale.US).format(new Date(System.currentTimeMillis())));
                alerts.setMedicineName(medicineName);
                Alerts.addToDB(alerts);
                Intent alertCount = new Intent(MainActivity.ALERT_COUNT_INCREASED_INDICATOR);
                LocalBroadcastManager.getInstance(context).sendBroadcast(alertCount);
            }
        }

    }

    private static boolean containsAlerts(List<Alerts> c, String artype) {
        for (Alerts o : c) {
            if (o != null && o.getAlertType().equals(artype)) {
                return true;
            }
        }
        return false;
    }

    public static void sendBatteryLogToServer(Context context, String status, String description) {
        LogUtils.debug("Forming Battery log to send to server...");

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.accumulate("missedTime", new SimpleDateFormat("HH:mm:ss", Locale.US).format(new Date(System.currentTimeMillis())));
            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
            jsonObject.accumulate("missedTimeSlot", "");
            jsonObject.accumulate("missedBeforeOrAfterFood", "");
            jsonObject.accumulate("alertType", status);
            jsonObject.accumulate("alertDescription", description);
            jsonObject.accumulate("missedMedicationIds", "");
            jsonObject.accumulate("createdDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis())));

            LogUtils.debug("Log formed. Pushing to server.");

            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.MISSED_MEDICATION_URL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }

    }


    public static void sendCallDuration(Context context, String status, String description, String phoneNumber, double duration) {
        LogUtils.debug("Forming Call Duration log to send to server...");

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.accumulate("missedTime", new SimpleDateFormat("HH:mm:ss", Locale.US).format(new Date(System.currentTimeMillis())));
            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
            jsonObject.accumulate("missedTimeSlot", "");
            jsonObject.accumulate("missedBeforeOrAfterFood", "");
            jsonObject.accumulate("alertType", status);
            jsonObject.accumulate("alertDescription", description);
            jsonObject.accumulate("missedMedicationIds", "");
            jsonObject.accumulate("phoneNumber", phoneNumber);
            jsonObject.accumulate("callDuration", duration);
            jsonObject.accumulate("createdDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis())));

            LogUtils.debug("Log formed. Pushing to server.");

            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.MISSED_MEDICATION_URL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }

    }

    static void sendSWUpdateReportToServer(Context context, String status, String reason) {
        LogUtils.debug("preparing SW update status report server...");
        String versionRelease = Build.VERSION.RELEASE;
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.accumulate("date", new SimpleDateFormat("mm:dd:yyyy", Locale.US).format(new Date(System.currentTimeMillis())));
            jsonObject.accumulate("appVersion", versionRelease + "_" + SoftwareUpdateUtil.getAppVersion(context));
            jsonObject.accumulate("imei", Globals.imei);
            jsonObject.accumulate("reason", reason);
            jsonObject.accumulate("status", status);

            LogUtils.debug("SW update status formed. Pushing to server.");

            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.SW_UPDATE_REPORT_URL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }

    }

    public static void sendBroadcastForSubscription() {
        LogUtils.debug("Subscription failed event has been received");
        CommUtils.deleteDatabase();
        Intent alertCount = new Intent(MainActivity.SUBSCRIPTION_FAILED);
        LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
    }

    public static void deleteDatabase() {
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/";
        File root = new File(rootPath);
        deleteDirectory(root);
    }

    private static boolean deleteDirectory(File path) {
        if (path.exists()) {
            File[] files = path.listFiles();
            if (files == null) {
                return true;
            }
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectory(file);
                } else {
                    file.delete();
                }
            }
        }
        return (path.delete());
    }

    public static void enableBroadcastReceiver(Context context) {

        LogUtils.debug("Enabling all the BroadcastReceiver receivers");
        ComponentName receiver = new ComponentName(WatchApp.getContext(), InternetStatusChangeReceiver.class);
        PackageManager pm = context.getPackageManager();
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), MedicationScheduleSetupReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), ReminderReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), TimeZoneChangedReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), CustomAlertReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), ScheduleMessageReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), MidnightPedoMeterResetReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), SendPedoMeterReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), VitalReminderReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), BleBroadCastReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), CheckQueueReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP);
    }

    public static void disableBroadcastReceiver(Context context) {
        LogUtils.debug("Disabling all the BroadcastReceiver receivers");
        ComponentName receiver = new ComponentName(WatchApp.getContext(), InternetStatusChangeReceiver.class);
        PackageManager pm = context.getPackageManager();
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), MedicationScheduleSetupReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), ReminderReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), TimeZoneChangedReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), CustomAlertReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), ScheduleMessageReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), MidnightPedoMeterResetReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), SendPedoMeterReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);
        receiver = new ComponentName(WatchApp.getContext(), VitalReminderReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), BleBroadCastReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);

        receiver = new ComponentName(WatchApp.getContext(), CheckQueueReceiver.class);
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP);
    }

    public static String getDeviceName() {
        String manufacturer = Build.MANUFACTURER;
        String model = Build.MODEL;
        if (model.startsWith(manufacturer)) {
            return capitalize(model);
        } else {
            return capitalize(manufacturer) + " " + model;
        }
    }


    private static String capitalize(String s) {
        if (s == null || s.length() == 0) {
            return "";
        }
        char first = s.charAt(0);
        if (Character.isUpperCase(first)) {
            return s;
        } else {
            return Character.toUpperCase(first) + s.substring(1);
        }
    }


    public static boolean isAppIsInBackground(Context context) {
        boolean isInBackground = true;
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT_WATCH) {
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = am.getRunningAppProcesses();
            for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                if (processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                    for (String activeProcess : processInfo.pkgList) {
                        if (activeProcess.equals(context.getPackageName())) {
                            isInBackground = false;
                        }
                    }
                }
            }
        } else {
            List<ActivityManager.RunningTaskInfo> taskInfo = am.getRunningTasks(1);
            ComponentName componentInfo = taskInfo.get(0).topActivity;
            if (componentInfo.getPackageName().equals(context.getPackageName())) {
                isInBackground = false;
            }
        }

        return isInBackground;
    }

    public static int getBatteryPercentage(Context context) {

        IntentFilter iFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatus = context.registerReceiver(null, iFilter);

        int level = batteryStatus != null ? batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) : -1;
        int scale = batteryStatus != null ? batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1) : -1;

        float batteryPct = level / (float) scale;

        return (int) (batteryPct * 100);
    }


    public static void sendHeartLogToServer(Context context, String heartRateValue, String status, int vitalScheduleId) {
        JSONObject jsonObject = new JSONObject();
        try {
            if (status.equalsIgnoreCase("success")) {

                JSONArray jsonArray = new JSONArray();
                JSONObject jsonObj = new JSONObject();
                jsonObj.put("vitalTypeName", "Heart Rate");
                jsonObj.put("vitalValue", heartRateValue);
                jsonArray.put(jsonObj);

                jsonObject.accumulate("vitalTypeValueVOList", jsonArray);
                jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
                jsonObject.accumulate("measuredDateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(new Date(System.currentTimeMillis())));
                jsonObject.accumulate("status", "Success");
                jsonObject.accumulate("vitalScheduleId", vitalScheduleId);
            } else {
                jsonObject.accumulate("status", "Error");
                jsonObject.accumulate("errorDescription", "Error While Collecting " + "Heart Rate" + " Vital Data");
                jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
                jsonObject.accumulate("measuredDateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(new Date(System.currentTimeMillis())));
            }
            LogUtils.debug("Log formed. Pushing to server." + jsonObject.toString());
            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.VITAL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }
    }

    public static void sendTextMessageResponseLogToServer(Context context, String questionId, String answer) {
        LogUtils.debug("Forming sendTextMessageResponseLogToServer ...");

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
            jsonObject.accumulate("createdDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis())));
            jsonObject.accumulate("questionId", questionId);
            jsonObject.accumulate("answer", answer);

            LogUtils.debug("Log formed. Pushing to server.");

            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.TEXT_MESSAGE_RESPONSE_URL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }
    }

    public static void sendScheduledMessageResponseLogToServer(Context context, String questionId, String answer) {
        LogUtils.debug("Forming sendScheduledMessageResponseLogToServer ...");

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
            jsonObject.accumulate("createdDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis())));
            jsonObject.accumulate("questionId", questionId);
            jsonObject.accumulate("answer", answer);

            LogUtils.debug("Log formed. Pushing to server.");

            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.SCHEDULED_MESSAGE_RESPONSE_URL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }
    }

    public static void sendPedoMeterLogToServer(Context context, int count, boolean isReset, boolean isLastRecord) {
        LogUtils.debug("Forming sendPedoMeterLogToServer ...steps " + count);

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
            jsonObject.accumulate("createdDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis())));
            jsonObject.accumulate("count", count);
            jsonObject.accumulate("isReset", isReset);
            jsonObject.accumulate("isLastRecord", isLastRecord);
            LogUtils.debug("Log formed. Pushing to server.");
            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.PEDOMETER_URL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }
    }

    public static void showToastMessage(String message, Context context) {

        LogUtils.debug("Showing Toast :" + message + " With Context :" + context);
        try {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
        } catch (WindowManager.BadTokenException e) {
            Log.e("WindowManagerBad ", e.toString());
            LogUtils.debug("Unable to show Toast message " + e.getMessage());
        }
    }

    public static void sendVitalDataToServer(Context context, JSONArray vitalData, int vitalScheduleId, String status, String measuredDateTime, String errorVitalTypeName) {

        JSONObject jsonObject = new JSONObject();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
            if (measuredDateTime == null) {
                measuredDateTime = sdf.format(new Date(System.currentTimeMillis()));
            }
            Date currentDate = new Date(System.currentTimeMillis());
            Log.e("Current Date :", "" + currentDate);
            Date measureDate = sdf.parse(measuredDateTime);
            Log.e("Measure Date :", "" + measureDate);
            long difference_In_Time = currentDate.getTime() - measureDate.getTime();
            long difference_In_Years = (difference_In_Time / (1000L * 60 * 60 * 24 * 365));
            Log.e("Difference In Years :", "" + difference_In_Years);
            if (difference_In_Years > 1) {
                measuredDateTime = sdf.format(new Date(System.currentTimeMillis()));
                Log.e("After Calculation :", "" + measuredDateTime);
            }
            if (status.equalsIgnoreCase("success")) {
                jsonObject.accumulate("vitalTypeValueVOList", vitalData);
                jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
                jsonObject.accumulate("measuredDateTime", measuredDateTime);
                jsonObject.accumulate("status", "Success");
                jsonObject.accumulate("vitalScheduleId", vitalScheduleId);
            } else {
                jsonObject.accumulate("status", "Error");
                jsonObject.accumulate("errorDescription", "Error While Collecting " + errorVitalTypeName + "Vital Data");
                jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
                jsonObject.accumulate("measuredDateTime", measuredDateTime);
            }
            LogUtils.debug("Log formed. Pushing to server." + jsonObject.toString());
            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.VITAL);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }
    }

    public static void sendSleepMonitorToServer(Context context, JSONObject jsonObject) {
        LogUtils.debug("Forming sendSleepMonitorToServer  " + jsonObject.toString());
        try {
            CommUtils.pushToServer(context, jsonObject.toString(), URLConstants.SLEEP_MONITOR);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.debug("Got this exception: \n" + e.getMessage());
        }
    }
}
