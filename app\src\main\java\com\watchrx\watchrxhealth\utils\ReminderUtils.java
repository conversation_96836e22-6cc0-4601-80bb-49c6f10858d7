package com.watchrx.watchrxhealth.utils;

import static com.watchrx.watchrxhealth.constants.CommonConstants.REMINDER_TIMEOUT;
import static com.watchrx.watchrxhealth.constants.CommonConstants.REMINDER_TIMEOUT_FOR_FIXED;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.annotation.Nullable;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.CustomerAlertsDB;
import com.watchrx.watchrxhealth.db.MedicationScheduleInstance;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.db.VitalConfiguration;
import com.watchrx.watchrxhealth.db.VitalStatusDetails;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.receivers.BleBroadCastReceiver;
import com.watchrx.watchrxhealth.receivers.CheckQueueReceiver;
import com.watchrx.watchrxhealth.receivers.CustomAlertReceiver;
import com.watchrx.watchrxhealth.receivers.HrtBroadcastReceiver;
import com.watchrx.watchrxhealth.receivers.MidnightLogFileUploadReceiver;
import com.watchrx.watchrxhealth.receivers.MidnightPedoMeterResetReceiver;
import com.watchrx.watchrxhealth.receivers.NetworkHeartBeat;
import com.watchrx.watchrxhealth.receivers.ReminderReceiver;
import com.watchrx.watchrxhealth.receivers.ScheduleMessageReceiver;
import com.watchrx.watchrxhealth.receivers.SendPedoMeterReceiver;
import com.watchrx.watchrxhealth.receivers.VitalReminderReceiver;
import com.watchrx.watchrxhealth.syncup.MedicationScheduleSetupReceiver;
import com.watchrx.watchrxhealth.syncup.SyncupReceiver;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Random;

public class ReminderUtils {

    public static void setUpSyncUpdaterAlarm11(Context context, int h, int m) {
        Intent intentAlarm = new Intent(context, MedicationScheduleSetupReceiver.class);
        intentAlarm.putExtra("TimeToSync", "DayTime");
        PendingIntent pendingIntent;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getBroadcast(
                    context,
                    11, intentAlarm,
                    PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, 11, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                pendingIntent = PendingIntent.getBroadcast(
                        context,
                        11, intentAlarm,
                        PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, 11, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (Globals.dailySyncUpdaterat11 != null) {
            alarmManager.cancel(Globals.dailySyncUpdaterat11);
        }
        Globals.dailySyncUpdaterat11 = pendingIntent; // next save this for future use in clearing it if necessary
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, h);
        calendar.set(Calendar.MINUTE, m);
        calendar.set(Calendar.SECOND, 0);
        LogUtils.debug("Going to set up the schedule alarm for sync to server, which trigger at " + " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]");
//        alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    public static void setUpSyncUpdaterAlarm16(Context context, int h, int m) {
        Intent intentAlarm = new Intent(context, MedicationScheduleSetupReceiver.class);
        intentAlarm.putExtra("TimeToSync", "DayTime");
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 16, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getBroadcast(context, 16, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, 16, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                pendingIntent = PendingIntent.getBroadcast(context, 17, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, 17, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (Globals.dailySyncUpdaterat16 != null) {
            alarmManager.cancel(Globals.dailySyncUpdaterat16);
        }
        Globals.dailySyncUpdaterat16 = pendingIntent; // next save this for future use in clearing it if necessary
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, h);
        calendar.set(Calendar.MINUTE, m);
        calendar.set(Calendar.SECOND, 0);
        LogUtils.debug("Going to set up the schedule alarm for sync to server, which trigger at " + " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]");
//        alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    public static void setUpSyncUpdaterAlarm(Context context) {
        Intent intentAlarm = new Intent(context, SyncupReceiver.class);
        intentAlarm.putExtra("TimeToSync", "DayTime");
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 234, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getBroadcast(context, 234, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, 234, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, 235, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                pendingIntent = PendingIntent.getBroadcast(context, 234, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, 234, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (Globals.dailySyncUpdater != null) {
            alarmManager.cancel(Globals.dailySyncUpdater);
        }
        Globals.dailySyncUpdater = pendingIntent; // next save this for future use in clearing it if necessary
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());

        LogUtils.debug("Going to set up the schedule alarm for sync to server, which trigger at " + " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]");
        //alarmManager.setExact(AlarmManager.RTC_WAKEUP, System.currentTimeMillis(), pendingIntent);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    public static void setupDailyScheduleUpdater(Context context) {

        Intent intentAlarm = new Intent(context, MedicationScheduleSetupReceiver.class);
        intentAlarm.putExtra("TimeToSync", "NightTime");
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 3, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        pendingIntent = PendingIntent.getBroadcast(context, 3, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
            pendingIntent = PendingIntent.getBroadcast(context, 2, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

        // first cancel any previous scheduler
        if (Globals.dailyScheduleUpdater != null) {
            alarmManager.cancel(Globals.dailyScheduleUpdater);
        }

        Globals.dailyScheduleUpdater = pendingIntent; // next save this for future use in clearing it if necessary

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());

        int diff = 24 - calendar.get(Calendar.HOUR_OF_DAY);

        calendar.set(Calendar.SECOND, 5);
        calendar.set(Calendar.MINUTE, 1);
        calendar.add(Calendar.HOUR_OF_DAY, diff);
        LogUtils.debug("Going to set up the schedule alarm to trigger at 12:01:05 midnight for Sync");
        alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    public static void setupScheduleForToday(Context context) {
        LogUtils.debug("\n\tSetting up schedule for today");
        clearExistingReminderAlarms(context);
        MedicationScheduleInstance.deleteAllRows(); // clean the schedule table

        LogUtils.debug("Cleared existing schedules from database.");

        List<MedicationScheduleMaster> medicationDetailList = MedicationScheduleMaster.getFromDB();

        LogUtils.debug("We have " + medicationDetailList.size() + " schedules to set.");

        Globals.timersCurrentlySetMap.clear();
        for (MedicationScheduleMaster medicationDetail : medicationDetailList) {
            LogUtils.debug("\t- days: " + medicationDetail.getDaysOfWeek() + "\n\t- timeslots: " + medicationDetail.getTimeSlots() + "\n\t- Modifier: " + medicationDetail.getBeforeOrAfterFood());

            String[] days = medicationDetail.getDaysOfWeek().split("\\|");
            String[] times = medicationDetail.getTimeSlots().split("\\|");
            String[] quantities = medicationDetail.getQuantities().split("\\|");
            String[] beforeOrAfter = medicationDetail.getBeforeOrAfterFood().split("\\|");

            int i = 0;
            int j = 0;
            for (String day : days) {
                for (String time : times) {
                    if (beforeOrAfter[i].isEmpty()) {
                        scheduleMedication(context, medicationDetail.getMedicineId(), day, time, "Fixed", quantities[j]);
                        j++;
                    } else {
                        scheduleMedication(context, medicationDetail.getMedicineId(), day, time, gettimeModifier(beforeOrAfter[i]), quantities[i]);
                        i++;
                    }
                }
                i = 0;
                j = 0;
            }
        }

        List<CustomerAlertsDB> customerAlertsDBList = CustomerAlertsDB.getFromDB();
        for (CustomerAlertsDB alertsDB : customerAlertsDBList) {
            if (alertsDB.getType() != null && alertsDB.getType().equalsIgnoreCase("schedule")) {
                String startDate = alertsDB.getStartDate();
                String endDate = alertsDB.getEndDate();
                LogUtils.debug("While setting Custom Alert Start Date:" + startDate + " End Date: " + endDate + " Current Date :" + new Date());
                Log.w("CustomAlert", "While setting Custom Alert Start Date:" + startDate + " End Date: " + endDate + " Current Date :" + new Date());
                if (isDateValidForCustomAlert(startDate, endDate)) {
                    LogUtils.debug("Custom Alert Date is valid so we are going to setup alarm.");
                    Log.w("CustomAlert", "Custom Alert Date is valid so we are going to setup alarm.");
                    scheduleCustomAlert(context, alertsDB.getAlterTime(), alertsDB.getAlterType(), alertsDB.getAlterDetail());
                } else {
                    LogUtils.debug("Custom Alert Date is not valid so we are ignore this alarm.");
                    Log.w("CustomAlert", "Custom Alert Date is not valid so we are ignore this alarm.");
                }
            } else {
                scheduleCustomAlert(context, alertsDB.getAlterTime(), alertsDB.getAlterType(), alertsDB.getAlterDetail());
            }
        }
        setScheduleMessageAlarm(context);
        setUpVitalConfigurationReminder(context);
    }

    public static boolean isDateValidForCustomAlert(String dateStartStr, String dateEndStr) {
        if (dateStartStr != null && !dateStartStr.isEmpty() && dateEndStr != null && !dateEndStr.isEmpty()) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yyyy", Locale.US);
                Date currentDate = new Date();
                Date dateStart = sdf.parse(dateStartStr);
                Date dateEnd = sdf.parse(dateEndStr);
                if (dateStart != null && dateEnd != null) {
                    return (currentDate.after(dateStart) || currentDate.equals(dateStart)) && (currentDate.before(dateEnd));
                }
                return false;
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    public static void setUpVitalConfigurationReminder(Context context) {
        List<VitalConfiguration> configurationList = VitalConfiguration.getFromDB();
        LogUtils.debug("We have " + configurationList.size() + " Vitals reminder to set.");
        Log.w("VitalsCount:", "" + configurationList.size());
        clearVitalReminders(context);
        for (VitalConfiguration configuration : configurationList) {
            VitalStatusDetails vitalStatusDetails = VitalStatusDetails.getVitalStatusByVitalName(configuration.getVitalTypeName());
            if (vitalStatusDetails != null && vitalStatusDetails.getVitalStatus().equalsIgnoreCase("enable")) {
                boolean isValid = isDateValid(configuration.getStartDate(), configuration.getEndDate());
                if (isValid) {
                    String[] days = configuration.getScheduleDayOfWeek().split("\\|");
                    String collectMode = configuration.getCollectMode();
                    String vitalScheduleId = configuration.getVitalScheduleId();
                    String[] times = null;
                    String frequency = null;
                    if (collectMode.equalsIgnoreCase("time")) {
                        times = configuration.getTimeSlots().split("\\|");
                    } else {
                        frequency = configuration.getFrequency();
                    }
                    if (times != null && times.length > 0) {
                        for (String day : days) {
                            for (String time : times) {
                                setUpVitalIdUsingTimes(context, vitalScheduleId, day, time, collectMode);
                            }
                        }
                    }
                    if (frequency != null) {
                        for (String day : days) {
                            setUpVitalIdUsingFrequency(context, vitalScheduleId, day, frequency, collectMode);
                        }
                    }
                } else {
                    Log.w("Is Date Valid :", "Reminder didn't proceed Start Date : " + configuration.getStartDate() + "and End Date :" + configuration.getEndDate() + " is not matching with Current date : " + new Date());
                    LogUtils.debug("Reminder didn't proceed Start Date : " + configuration.getStartDate() + " and End Date :" + configuration.getEndDate() + " is not matching with Current date : " + new Date());
                }
            } else {
                assert vitalStatusDetails != null;
                Log.e("Vital Reminder", vitalStatusDetails.getVitalTypeName() + " Reminder didn't set due to Vital Status :" + vitalStatusDetails.getVitalStatus());
                LogUtils.debug(vitalStatusDetails.getVitalTypeName() + " Reminder didn't set due to Vital Status :" + vitalStatusDetails.getVitalStatus());
            }
        }
    }

    public static boolean isDateValid(String dateStartStr, String dateEndStr) {
        if (dateStartStr != null && !dateStartStr.isEmpty() && dateEndStr != null && !dateEndStr.isEmpty()) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
                Date currentDate = new Date();
                Date dateStart = sdf.parse(dateStartStr);
                Date dateEnd = sdf.parse(dateEndStr);
                if (dateStart != null && dateEnd != null) {
                    return (currentDate.after(dateStart) || currentDate.equals(dateStart)) && (currentDate.before(dateEnd));
                }
                return false;
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    public static void setUpVitalIdUsingFrequency(Context context, String vitalScheduleId, String day, String frequency, String collectMode) {
        int frequencyInInt = Integer.parseInt(frequency);
        Calendar cal = getCalendarForVitalFrequency(frequencyInInt, day);
        if (cal != null && (cal.getTimeInMillis() > System.currentTimeMillis())) {
            String time = cal.get(Calendar.HOUR_OF_DAY) + ":" + cal.get(Calendar.MINUTE);
            String key = (day + "-" + time + "-" + vitalScheduleId + "-" + collectMode).toLowerCase();
            if (!Globals.vitalsTimersCurrentlySetMap.containsKey(key)) {
                LogUtils.debug("There is no pre-existing alarm for this timeSlot. Need to Set up an alarm for Schedule Message");
                Intent reminderAlarm = new Intent(context, VitalReminderReceiver.class);
                reminderAlarm.putExtra("day", day);
                reminderAlarm.putExtra("time", time);
                reminderAlarm.putExtra("vitalScheduleId", vitalScheduleId);
                reminderAlarm.putExtra("collectMode", collectMode);
                reminderAlarm.putExtra("triggerAt", cal.getTimeInMillis());

                Globals.reminderAlarmCount++;
//                PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, 0);
                PendingIntent pendingIntent = null;
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
                } else {
                    pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
                }
                if (pendingIntent == null) {
                    LogUtils.debug("Pending intent intent getting null");
//                    pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                        pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
                    } else {
                        pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
                    }
                }
                Globals.vitalsIntentMap.put(key, pendingIntent);
                AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
                alarmManager.cancel(pendingIntent); // cancel any previous one

//                alarmManager.setExact(AlarmManager.RTC_WAKEUP, cal.getTimeInMillis(), pendingIntent);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, cal.getTimeInMillis(), pendingIntent);
                else
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, cal.getTimeInMillis(), pendingIntent);

                LogUtils.debug("Setting Vital Reminder to trigger at:" + cal.getTimeInMillis()
                        + " [" + cal.get(Calendar.HOUR_OF_DAY) + ":" + cal.get(Calendar.MINUTE) + ":" + cal.get(Calendar.SECOND) + "] for "
                        + " Day : " + day + " Time : " + time + " vitalScheduleId : " + vitalScheduleId + " collectMode : " +
                        collectMode + " with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent + " And Key :" + key);
                Globals.vitalsTimersCurrentlySetMap.put(key, true);
            }
        }
    }

    private static void setUpVitalIdUsingTimes(Context context, String vitalScheduleId, String day, String time, String collectMode) {
        Calendar cal = getFixedCalendar(time, day);
        if (cal != null && (cal.getTimeInMillis() > (System.currentTimeMillis()))) {
            String key = (day + "-" + time + "-" + vitalScheduleId + "-" + collectMode).toLowerCase();
            if (!Globals.vitalsTimersCurrentlySetMap.containsKey(key)) {
                LogUtils.debug("There is no pre-existing alarm for this timeSlot. Need to Set up an alarm for Schedule Message");
                setUpVitalReminder(context, cal, day, time, vitalScheduleId, collectMode);
                Globals.vitalsTimersCurrentlySetMap.put(key, true);
            }
        }
    }

    public static void setUpVitalReminder(Context context, Calendar cal, String day, String time, String vitalScheduleId, String collectMode) {
        if (cal != null && (cal.getTimeInMillis() > System.currentTimeMillis())) {
            LogUtils.debug("There is no pre-existing alarm for this timeSlot. Need to Set up an alarm for Schedule Message");

            Intent reminderAlarm = new Intent(context, VitalReminderReceiver.class);
            reminderAlarm.putExtra("day", day);
            reminderAlarm.putExtra("time", time);
            reminderAlarm.putExtra("vitalScheduleId", vitalScheduleId);
            reminderAlarm.putExtra("collectMode", collectMode);
            reminderAlarm.putExtra("triggerAt", cal.getTimeInMillis());

            Globals.reminderAlarmCount++;
//            PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, 0);
            PendingIntent pendingIntent = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            if (pendingIntent == null) {
                LogUtils.debug("Pending intent intent getting null");
//                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                    pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
                } else {
                    pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
                }
            }
            String key = (day + "-" + time + "-" + vitalScheduleId + "-" + collectMode).toLowerCase();
            Globals.vitalsIntentMap.put(key, pendingIntent);

            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            alarmManager.cancel(pendingIntent); // cancel any previous one
//            alarmManager.setExact(AlarmManager.RTC_WAKEUP, cal.getTimeInMillis(), pendingIntent);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, cal.getTimeInMillis(), pendingIntent);
            else
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, cal.getTimeInMillis(), pendingIntent);

            LogUtils.debug("Setting Vital Reminder to trigger at:" + cal.getTimeInMillis()
                    + " [" + cal.get(Calendar.HOUR_OF_DAY) + ":" + cal.get(Calendar.MINUTE) + ":" + cal.get(Calendar.SECOND) + "] for "
                    + " Day : " + day + " Time : " + time + " vitalScheduleId : " + vitalScheduleId + "collectMode : " +
                    collectMode + " with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent + " And Key :" + key);
        }
    }


    public static void setScheduleMessageAlarm(Context context) {
        List<ScheduleMessagesDB> scheduleMessage = ScheduleMessagesDB.getFromDB();
        LogUtils.debug("We have " + scheduleMessage.size() + " schedules Message to set.");
        for (ScheduleMessagesDB messagesDB : scheduleMessage) {
            String[] days = messagesDB.getDayOfWeek().split("\\|");
            String[] times = messagesDB.getTimeSlots().split("\\|");
            for (String day : days) {
                for (String time : times) {
                    setAlarmForScheduleMessage(context, day, time, messagesDB.getQuestionId());
                }
            }
        }
    }

    private static void setAlarmForScheduleMessage(Context context, String day, String time, String questionId) {
        Calendar cal = getFixedCalendar(time, day);
        if (cal != null && (cal.getTimeInMillis() > (System.currentTimeMillis()))) {
            if (!Globals.timersCurrentlySetMap.containsKey((day + time + questionId).toLowerCase())) {
                LogUtils.debug("There is no pre-existing alarm for this time-0slot. Need to Set up an alarm for Schedule Message");
                setScheduleMessageReminderAlarm(context, cal, day, time, questionId);
                Globals.timersCurrentlySetMap.put((day + time + questionId).toLowerCase(), true);
            }
        }
    }

    private static void setScheduleMessageReminderAlarm(Context context, Calendar calendar, String day, String time, String questionId) {

        Intent reminderAlarm = new Intent(context, ScheduleMessageReceiver.class);
        reminderAlarm.putExtra("day", day);
        reminderAlarm.putExtra("time", time);
        reminderAlarm.putExtra("questionId", questionId);

        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, 0);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        String key = day + "-" + time + questionId;
        Globals.intentMap.put(key, pendingIntent);

        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.cancel(pendingIntent);
        alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    private static void scheduleCustomAlert(Context context, String alertTime, String alertType, String alertDetail) {
        Calendar cal = getCustomAlertCalendar(alertTime);
        if (cal.getTimeInMillis() > System.currentTimeMillis()) {
            if (!Globals.timersCurrentlySetMap.containsKey((alertTime + alertType + alertDetail).toLowerCase())) {
                LogUtils.debug("There is no pre-existing alarm for this timeslot. Need to Set up an alarm");
                setCustomReminderAlarm(context, cal, alertType, alertDetail);
                Globals.timersCurrentlySetMap.put((alertTime + alertType + alertDetail).toLowerCase(), true);
            }
        }
    }

    private static void setCustomReminderAlarm(Context context, Calendar calendar, String alertType, String alertDetail) {

        Intent reminderAlarm = new Intent(context, CustomAlertReceiver.class);
        reminderAlarm.putExtra("alertType", alertType);
        reminderAlarm.putExtra("alertDetail", alertDetail);
        reminderAlarm.putExtra("triggerAt", calendar.getTimeInMillis());

        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, 0);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        String key = alertType + "-" + alertDetail;
        Globals.intentMap.put(key, pendingIntent);

        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.cancel(pendingIntent); // cancel any previous one

        LogUtils.debug("Setting Reminder Alarm to trigger at:" + calendar.getTimeInMillis() + " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "] for " + alertType + "-" + alertDetail + " with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);

//        alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }


    public static Calendar getCustomAlertCalendar(String time) {
        String[] timesArray = time.split(":");
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timesArray[0]));
        calendar.set(Calendar.MINUTE, Integer.parseInt(timesArray[1]));
        calendar.set(Calendar.SECOND, 0);
        return calendar;
    }

    private static String gettimeModifier(String str) {
        if (str.equalsIgnoreCase("Anytime")) {
            return "Any";
        } else if (str.equalsIgnoreCase("")) {
            return "Fixed";
        } else {
            return str.replace("Food", "");
        }
    }

    private static void scheduleMedication(Context context, String medicineId, String day, String time, String timeModifier, String quantity) {
        Calendar cal = getMedicationTime(day, time, timeModifier);

        if (cal != null && cal.getTimeInMillis() > System.currentTimeMillis() && time != null && !time.isEmpty() && !timeModifier.isEmpty()) {
            LogUtils.debug("cal.getTimeInMillis: " + cal.getTimeInMillis() + "  System.currentTimeMillis: " + System.currentTimeMillis());
            MedicationScheduleInstance scheduleInstance = new MedicationScheduleInstance();

            scheduleInstance.setMedicineID(medicineId);
            scheduleInstance.setTimeSlot(time);
            scheduleInstance.setBeforeAfterFood(timeModifier);
            scheduleInstance.setQuantities(quantity);

            LogUtils.debug("Scheduled an Instance in DB: [" + medicineId + ", " + time + ", " + timeModifier + "]");
            MedicationScheduleInstance.addToDB(scheduleInstance);

            if (!Globals.timersCurrentlySetMap.containsKey((day + time + timeModifier).toLowerCase())) {
                LogUtils.debug("There is no pre-existing alarm for this timeslot. Need to Set up an alarm");
                setReminderAlarm(context, cal, time, timeModifier);
                Globals.timersCurrentlySetMap.put((day + time + timeModifier).toLowerCase(), true);
            }
        }
    }

    private static int getDayOfWeek(String day) {
        if ("Mo".equalsIgnoreCase(day)) {
            return Calendar.MONDAY;
        } else if ("Tu".equalsIgnoreCase(day)) {
            return Calendar.TUESDAY;
        } else if ("We".equalsIgnoreCase(day)) {
            return Calendar.WEDNESDAY;
        } else if ("Th".equalsIgnoreCase(day)) {
            return Calendar.THURSDAY;
        } else if ("Fr".equalsIgnoreCase(day)) {
            return Calendar.FRIDAY;
        } else if ("Sa".equalsIgnoreCase(day)) {
            return Calendar.SATURDAY;
        } else if ("Su".equalsIgnoreCase(day)) {
            return Calendar.SUNDAY;
        }

        return -1;
    }

    public static Calendar getMedicationTime(String day, String time, String timeModifier) {
        PatientDetails patientDetail = PatientDetails.getFromDB();
        Calendar calendar = null;
        if (timeModifier.equalsIgnoreCase("Fixed")) {
            if (time.isEmpty()) {
                return calendar;
            } else {
                calendar = getFixedCalendar(time, day);
            }
        } else {
            calendar = getRegularCalendar(day, time, timeModifier, patientDetail);
        }
        return calendar;
    }

    @Nullable
    private static Calendar getRegularCalendar(String day, String time, String timeModifier, PatientDetails patientDetail) {
        int hour = patientDetail.getHourOfChosenTimeForTimeSlot(time);
        int minute = patientDetail.getMinutesOfChosenTimeForTimeSlot(time);

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, 5);

        if ("Before".equalsIgnoreCase(timeModifier)) {
            calendar.add(Calendar.MINUTE, -30);
        } else if ("After".equalsIgnoreCase(timeModifier)) {
            calendar.add(Calendar.MINUTE, 30);
        }
        if (getDayOfWeek(day) == calendar.get(Calendar.DAY_OF_WEEK)) {
            return calendar;
        } else {
            return null;
        }
    }

    @Nullable
    public static Calendar getFixedCalendar(String time, String day) {
        String[] s = time.split(":");
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(s[0]));
        calendar.set(Calendar.MINUTE, Integer.parseInt(s[1]));
        calendar.set(Calendar.SECOND, 0);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        if (getDayOfWeek(day) == dayOfWeek) {
            return calendar;
        } else {
            return null;
        }
    }

    @Nullable
    private static Calendar getCalendarForVitalFrequency(int frequency, String day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        int currentMinutes = calendar.get(Calendar.MINUTE);
        int correctionMinutes = currentMinutes % frequency;
        int actualMinutesToRoundUp = currentMinutes + (frequency - correctionMinutes);
        calendar.set(Calendar.MINUTE, actualMinutesToRoundUp);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        if (getDayOfWeek(day) == dayOfWeek) {
            return calendar;
        } else {
            return null;
        }
    }

    private static PendingIntent setReminderAlarm(Context context, Calendar calendar, String timeSlot, String timeModifier) {
        return setReminderAlarm(context, calendar, timeSlot, timeModifier, 1);
    }

    public static PendingIntent setReminderAlarm(Context context, Calendar calendar, String timeSlot, String timeModifier, int timesAlreadyCalled) {
        Intent reminderAlarm = new Intent(context, ReminderReceiver.class);

        Random random = new Random(System.currentTimeMillis());
        String correlationId = random.nextLong() + "-" + random.nextLong();

        reminderAlarm.putExtra("beforeOrAfterFood", timeModifier);
        reminderAlarm.putExtra("timeSlot", timeSlot);
        reminderAlarm.putExtra("triggerAt", calendar.getTimeInMillis());
        reminderAlarm.putExtra("timesAlreadyCalled", timesAlreadyCalled);
        // reminderAlarm.putExtra("correlationId", correlationId);

        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, 0);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        String key = timeModifier + "-" + timeSlot;
        Globals.intentMap.put(key, pendingIntent);

        // LogUtils.debug("Scheduling an alarm with:\n\t- Correlation Id: '" + correlationId + "'\n\t- key: '" + key + "'\n\t- to Expire At: " + calendar.getTimeInMillis());
        Globals.alarmCorrelationIdMap.put(key, correlationId);

        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.cancel(pendingIntent); // cancel any previous one

        LogUtils.debug("Setting Reminder Alarm to trigger at:" + calendar.getTimeInMillis() + " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "] for " + timeModifier + "-" + timeSlot + " with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);

//        alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        return pendingIntent;
    }


    public static void clearExistingReminderAlarms(Context context) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        for (String key : Globals.intentMap.keySet()) {
            PendingIntent pendingIntent = Globals.intentMap.get(key);
            LogUtils.debug("\n\tClearing an alarm" + pendingIntent);
            alarmManager.cancel(pendingIntent);
            if (pendingIntent != null) {
                pendingIntent.cancel();
            }
        }
        if (Globals.reminderAlertTimeoutIntent != null) {
            Globals.reminderAlertTimeoutIntent.cancel();

            LogUtils.debug("\n\tClearing an 15 min alarm" + Globals.reminderAlertTimeoutIntent);
        }
        if (Globals.dailyScheduleMidnightUpdater != null) {
            Globals.dailyScheduleMidnightUpdater.cancel();
            LogUtils.debug("\n\tClearing an Midnight sync every 30min alarm" + Globals.dailyScheduleMidnightUpdater);
        }
        if (Globals.dailySyncUpdater != null) {
            Globals.dailySyncUpdater.cancel();
            LogUtils.debug("\n\tClearing an DayTime sync alarm" + Globals.dailySyncUpdater);
        }
        if (Globals.dailySyncUpdaterat11 != null) {
            Globals.dailySyncUpdaterat11.cancel();
            LogUtils.debug("\n\tClearing an DayTime sync alarm@11" + Globals.dailySyncUpdaterat11);
        }
        if (Globals.dailySyncUpdaterat16 != null) {
            Globals.dailySyncUpdaterat16.cancel();
            LogUtils.debug("\n\tClearing an DayTime sync alarm@16" + Globals.dailySyncUpdaterat16);
        }
        Globals.intentMap.clear();
        Globals.alarmCorrelationIdMap.clear();
        clearVitalReminders(context);
    }

    private static void clearVitalReminders(Context context) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        Globals.vitalsTimersCurrentlySetMap.clear();
        for (String key : Globals.vitalsIntentMap.keySet()) {
            PendingIntent pendingIntent = Globals.vitalsIntentMap.get(key);
            LogUtils.debug("\n\tClearing an vital alarm" + pendingIntent);
            alarmManager.cancel(pendingIntent);
            if (pendingIntent != null) {
                pendingIntent.cancel();
            }
        }
        Globals.vitalsIntentMap.clear();
    }


    public static void clearExistingDailyUpdaterAlarm(Context context) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

        // first cancel any previous scheduler
        if (Globals.dailyScheduleUpdater != null) {
            alarmManager.cancel(Globals.dailyScheduleUpdater);

            LogUtils.debug("\n\tclearExistingDailyUpdaterAlarm" + Globals.dailyScheduleUpdater);

            Globals.dailyScheduleUpdater = null;
        }
    }

    public static void next15MinReminder(Long triggerAt, int timesAlreadyCalled, String timeSlot, String beforeOrAfterFood) {
        LogUtils.debug("Will set a reminder one more time to trigger at: " + triggerAt + REMINDER_TIMEOUT);
        timesAlreadyCalled++;
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis() + REMINDER_TIMEOUT);

        Globals.reminderAlertTimeoutIntent = ReminderUtils.setReminderAlarm(WatchApp.getContext(), calendar, timeSlot, beforeOrAfterFood, timesAlreadyCalled);
        LogUtils.debug("15 min AlertReminder is set at " + timeSlot + beforeOrAfterFood + ". It has already been called " + (timesAlreadyCalled - 1) + " times.");

        if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
            GeneralUtils.speak("Alright. I'll try REMINDING you again, in 15 minutes ");
        } else {
            GeneralUtils.speak("Esta bien. Le RECORDARÉ otra vez, en aproximadamente 15 minutos");
        }

    }

    public static void next2MinReminder(Long triggerAt, int timesAlreadyCalled, String timeSlot, String beforeOrAfterFood) {
        LogUtils.debug("Will set a reminder one more time to trigger at: " + triggerAt + REMINDER_TIMEOUT_FOR_FIXED);
        timesAlreadyCalled++;
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis() + REMINDER_TIMEOUT_FOR_FIXED);
        Globals.reminderAlertTimeoutIntent = ReminderUtils.setReminderAlarm(WatchApp.getContext(), calendar, timeSlot, beforeOrAfterFood, timesAlreadyCalled);
        LogUtils.debug("2min 30 sec AlertReminder is set at " + timeSlot + beforeOrAfterFood + ". It has already been called " + (timesAlreadyCalled - 1) + " times.");

        if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
            GeneralUtils.speak("Alright. I'll try REMINDING you again, in 2 minutes 30 seconds");
        } else {
            GeneralUtils.speak("Esta bien. Le RECORDARÉ otra vez, en aproximadamente 2 minutos y 30 segundos.");
        }

    }


    private static void setUpHeartRateScheduleReminder(Context context) {
        LogUtils.debug("Setting up reminder for Heart Rate Collection");
        PatientDetails details = PatientDetails.getFromDB();
        if (details.getHeartRateStatus().equalsIgnoreCase("enable")) {
            LogUtils.debug("Setting up reminder for Heart Rate Collection");
            if (details.getHeartScheduleDays() != null && !details.getHeartScheduleDays().isEmpty() && details.getHeartRateScheduleTimeSlots() != null &&
                    !details.getHeartRateScheduleTimeSlots().isEmpty()) {
                String[] days = details.getHeartScheduleDays().split("\\|");
                String[] times = details.getHeartRateScheduleTimeSlots().split("\\|");
                for (String day : days) {
                    for (String time : times) {
                        if (day != null && !day.isEmpty() && time != null && !time.isEmpty()) {
                            Calendar calender = getFixedCalendar(time, day);
                            if (calender != null && (calender.getTimeInMillis() > (System.currentTimeMillis()))) {
                                if (!Globals.timersCurrentlySetMap.containsKey((day + time).toLowerCase())) {
                                    System.out.println("need to set Heart Rate Reminder for" + day + "-" + time);
                                    setHeartRateReminders(context, calender, day, time);
                                    Globals.timersCurrentlySetMap.put((day + time).toLowerCase(), true);
                                }

                            }
                        }
                    }
                }
            }

        } else {
            LogUtils.debug("HeartRate is not Enabled From Server");
        }
    }

    private static void setHeartRateReminders(Context context, Calendar calendar, String day, String time) {
        Intent reminderAlarm = new Intent(context, HrtBroadcastReceiver.class);
        reminderAlarm.putExtra("triggerAt", calendar.getTimeInMillis());
        reminderAlarm.putExtra("day", day);
        reminderAlarm.putExtra("time", time);

        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, 0);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        String key = day + "-" + time;
        Globals.intentMap.put(key, pendingIntent);

        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.cancel(pendingIntent); // cancel any previous one

        LogUtils.debug("Setting Reminder Alarm for Heart Ratre to trigger at:" + calendar.getTimeInMillis() +
                " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]" +
                "  for " + day + "-" + time + " with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);

//        alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    public static void setReminderForHeartBeatPingToNetwork(Context context) {

        Intent reminderAlarm = new Intent(context, NetworkHeartBeat.class);
        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (Globals.networkHeartBeat != null) {
            alarmManager.cancel(Globals.networkHeartBeat);
        }
        Globals.networkHeartBeat = pendingIntent;

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.add(Calendar.MINUTE, 1);

//        alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    public static void setReminderToSendStepsToServer(Context context) {
        Intent reminderAlarm = new Intent(context, SendPedoMeterReceiver.class);
        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, reminderAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (Globals.pedoMeter != null) {
            alarmManager.cancel(Globals.pedoMeter);
            Globals.pedoMeter.cancel();
            Globals.pedoMeter = null;
        }
        Globals.pedoMeter = pendingIntent;

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.set(Calendar.SECOND, 0);
        if (PatientDetails.getFromDB().getPedoMeterInterval() != null && !PatientDetails.getFromDB().getPedoMeterInterval().isEmpty()) {
            int intervalTime = Integer.parseInt(PatientDetails.getFromDB().getPedoMeterInterval());
            calendar.add(Calendar.MINUTE, intervalTime);
        } else {
            calendar.add(Calendar.MINUTE, 59);
        }

        LogUtils.debug("Setting Reminder Alarm To Send Pedo Meter Counts to trigger at:" + calendar.getTimeInMillis() +
                " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]" +
                "   with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
//        } else {
//            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
//        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
    }

    public static void setupMidNightPedoMeterReset(Context context) {
        Intent intentAlarm = new Intent(context, MidnightPedoMeterResetReceiver.class);
        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

        // first cancel any previous scheduler
        if (Globals.pedoResetMeter != null) {
            alarmManager.cancel(Globals.pedoResetMeter);
            Globals.pedoResetMeter.cancel();
            Globals.pedoResetMeter = null;
        }

        Globals.pedoResetMeter = pendingIntent; // next save this for future use in clearing it if necessary

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        int diff = 24 - calendar.get(Calendar.HOUR_OF_DAY);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MINUTE, -1);
        calendar.add(Calendar.HOUR_OF_DAY, diff);

        if (calendar.getTimeInMillis() > System.currentTimeMillis()) {
            LogUtils.debug("Setting Reminder Alarm for Pedo Meter Reset to trigger at:" + calendar.getTimeInMillis() +
                    " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]" +
                    "   with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);

//            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
            else
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        }
    }

    public static void clearExistingPedoMeterAlarm(Context context) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

        // first cancel any previous scheduler
        if (Globals.pedoMeter != null) {
            alarmManager.cancel(Globals.pedoMeter);
            LogUtils.debug("\n\tclearExistingPedoMeterAlarm" + Globals.dailyScheduleUpdater);
            Globals.pedoMeter = null;
        }

        // first cancel any previous scheduler
        if (Globals.pedoResetMeter != null) {
            alarmManager.cancel(Globals.pedoResetMeter);
            LogUtils.debug("\n\tclearExistingPedoMeterResetAlarm" + Globals.dailyScheduleUpdater);
            Globals.pedoResetMeter = null;
        }
    }

    public static void setupReminderForLogFileUpload(Context context) {
        LogUtils.debug("Setting Up Reminder to Log File Upload at midnight");
        Intent intentAlarm = new Intent(context, MidnightLogFileUploadReceiver.class);
        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        // first cancel any previous scheduler
        if (Globals.midnightLogFileUpload != null) {
            alarmManager.cancel(Globals.midnightLogFileUpload);
            Globals.midnightLogFileUpload.cancel();
            Globals.midnightLogFileUpload = null;
        }

        Globals.midnightLogFileUpload = pendingIntent; // next save this for future use in clearing it if necessary

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        int diff = 24 - calendar.get(Calendar.HOUR_OF_DAY);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, -2);
        calendar.add(Calendar.HOUR_OF_DAY, diff);

        if (calendar.getTimeInMillis() > System.currentTimeMillis()) {
            LogUtils.debug("Setting Reminder Alarm for Log File Upload to trigger at:" + calendar.getTimeInMillis() +
                    " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]" +
                    "   with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
            else
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        }
    }

    public static void setupReminderForCollectVitals(Context context) {
        LogUtils.debug("Setting Up Reminder collect Vitals Ble Device Every 4hours");
        Intent intentAlarm = new Intent(context, BleBroadCastReceiver.class);
        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

        if (Globals.collectVitalsFromDeviceEvery4Hour != null) {
            alarmManager.cancel(Globals.collectVitalsFromDeviceEvery4Hour);
            Globals.collectVitalsFromDeviceEvery4Hour.cancel();
            Globals.collectVitalsFromDeviceEvery4Hour = null;
        }

        Globals.collectVitalsFromDeviceEvery4Hour = pendingIntent; // next save this for future use in clearing it if necessary

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.add(Calendar.HOUR, 1);
        calendar.set(Calendar.MINUTE, 5);
        calendar.set(Calendar.SECOND, 0);

        if (calendar.getTimeInMillis() > System.currentTimeMillis()) {
            LogUtils.debug("Setting Reminder Alarm for collect Vitals Ble Device Every 4hours at:" + calendar.getTimeInMillis() +
                    " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]" +
                    "   with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                Objects.requireNonNull(alarmManager).setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
            else
                Objects.requireNonNull(alarmManager).setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        }
    }

    public static void setupReminderForQueueCheck(Context context) {
        LogUtils.debug("Setting up and alarm to check Queue and due reminder clearance");
        Intent intentAlarm = new Intent(context, CheckQueueReceiver.class);
        Globals.reminderAlarmCount++;
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        if (pendingIntent == null) {
            LogUtils.debug("Pending intent intent getting null");
//            pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context, Globals.reminderAlarmCount, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
            }
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

        if (Globals.queueCheckReminderPendingIntent != null) {
            Objects.requireNonNull(alarmManager).cancel(Globals.queueCheckReminderPendingIntent);
            Globals.queueCheckReminderPendingIntent.cancel();
            Globals.queueCheckReminderPendingIntent = null;
        }

        Globals.queueCheckReminderPendingIntent = pendingIntent; // next save this for future use in clearing it if necessary

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.add(Calendar.HOUR, 1);
        calendar.set(Calendar.MINUTE, 3);
        calendar.set(Calendar.SECOND, 30);

        if (calendar.getTimeInMillis() > System.currentTimeMillis()) {
            LogUtils.debug("Setting up an alarm to check Queue and due reminder clearance at :" + calendar.getTimeInMillis() +
                    " [" + calendar.get(Calendar.HOUR_OF_DAY) + ":" + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND) + "]" +
                    "   with request code: " + Globals.reminderAlarmCount + " and pendingIntent: " + pendingIntent);
            if (alarmManager != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
                else
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
            }
        }
    }
}

